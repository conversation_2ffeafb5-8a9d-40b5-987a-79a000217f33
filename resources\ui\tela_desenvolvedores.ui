<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>tela_desenvolvedores</class>
 <widget class="QDialog" name="tela_desenvolvedores">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>427</width>
    <height>290</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>427</width>
    <height>290</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>427</width>
    <height>290</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:white;</string>
  </property>
  <widget class="QPushButton" name="return_button">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>220</y>
     <width>171</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>PointingHandCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    color:white;
	background-color: rgb(62, 91, 185);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(75, 107, 209);
}
QPushButton:pressed{
    background-color: rgb(36, 81, 224);
    border: 2px solid rgb(231, 231, 231);
}</string>
   </property>
   <property name="text">
    <string>Retornar</string>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>40</y>
     <width>61</width>
     <height>61</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>dev_icon.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>40</y>
     <width>61</width>
     <height>61</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>dev_icon.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>110</y>
     <width>161</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Rodrigo Sousa da Luz</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>130</y>
     <width>161</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Desenvolvedor Python</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>130</y>
     <width>161</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Desenvolvedor Python</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>290</x>
     <y>110</y>
     <width>101</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Gabriel Matias</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_12">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>250</y>
     <width>101</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>Logo_PGU.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="wordWrap">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_13">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>140</y>
     <width>61</width>
     <height>61</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>python.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="wordWrap">
    <bool>false</bool>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="resources/images/images.qrc"/>
 </resources>
 <connections/>
</ui>
