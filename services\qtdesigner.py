import os.path

from services.distribuidor import Distribuidor
from services.pgu import Regras_PGU

try:
    from PyQt5 import QtGui, QtWidgets, uic
except ImportError:
    from PyQt5 import QtGui, QtWidgets

    uic = None

import os
import re
import sys
import time
import traceback
from datetime import datetime
from multiprocessing import pool

import psutil
from decouple import Config, RepositoryEnv
from PyQt5 import QtGui, QtWidgets
from PyQt5.QtChart import QChart, QChartView, QPieSeries
from PyQt5.QtCore import *
from PyQt5.QtCore import (
    QCoreApplication,
    QDate,
    QObject,
    QRunnable,
    QThreadPool,
    pyqtSignal,
    pyqtSlot,
)
from PyQt5.QtGui import QIcon, QMovie, QPainter, QPixmap

from services.logger import LogMessage
from utils.resource_path import (
    get_env_path,
    get_graphics_path,
    get_ui_path,
)

config = Config(RepositoryEnv(get_env_path()))

EMAIL_SUPER = config("EMAIL_SUPER")
SENHA_SUPER: str = config("SENHA_SUPER")


class TelasDistribuidor:
    def __init__(self):
        self.leituraIntimacoes_finalizado = False
        data = datetime.now()
        if getattr(sys, "frozen", False):
            folder = os.path.dirname(sys.executable)
        else:
            folder = os.path.dirname(os.path.abspath(sys.modules["__main__"].__file__))
        if not os.path.exists(os.path.join(folder, "logs")):
            os.makedirs(os.path.join(folder, "logs"))

        nomeArquivoLog = f"R2D2_{data.year}_{data.month:0>2}_{data.day:0>2}__{data.hour:0>2}h{data.minute:0>2}min.log"
        nomeArquivoLog = os.path.join(folder, "logs", nomeArquivoLog)
        print(f"Log em {nomeArquivoLog}")
        self.__logger = LogMessage(filename=nomeArquivoLog, terminal=True)
        self.__time_i = 0.25
        print("Log ativado")
        self.__logger.log("-" * 40)
        self.__logger.log("-" * 40)
        self.__logger.log("INICIANDO UMA NOVA EXECUÇÃO DO R2D2")
        self.__logger.log("-" * 40)
        self.__logger.log("-" * 40)
        self.distribuidor = Distribuidor(self.__logger)
        self.__pgu = Regras_PGU(self.distribuidor)
        self.__logger.log("Criando objeto com regras de negócio da PGU")
        self.__sapiens_info = {}
        self.__sapiens_info["VERSAO"] = ""
        self.__regiao = ""
        self.__periodo = {}
        self.distribuir_todos = False
        self.distribuir_periodo = False
        self.sugeridos = None
        self.nao_distribuidos = None
        self.distribuidos = None
        self.erro_statusintegracao = None
        self.threadpool = QThreadPool()
        self.__logger.log(
            "Multithreading with máximo %d threads" % self.threadpool.maxThreadCount()
        )
        self.series = QPieSeries()
        self.chart = QChart()
        self.qpainter = QPainter()
        self.chartview = QChartView(self.chart)
        self.todos_processos = False
        self.processos_nao_integrados = False
        self.processos_integrados = False
        self.distribuir_etiquetar = False
        self.nao_distribuir_nao_etiquetar = False
        self.__logger.log("Carregando arquivos de telas")
        self.tela_inicial = uic.loadUi(get_ui_path("tela_inicial_escolhas.ui"))
        self.tela_desenvolvedores = uic.loadUi(get_ui_path("tela_desenvolvedores.ui"))
        self.tela_sucesso = uic.loadUi(get_ui_path("tela_sucesso.ui"))
        self.tela_principal = uic.loadUi(get_ui_path("tela_principal.ui"))
        self.tela_documentacao = uic.loadUi(get_ui_path("tela_documentacao.ui"))
        self.tela_carregamento = uic.loadUi(get_ui_path("tela_carregamento.ui"))
        self.gif_loading = QMovie(get_graphics_path("r2d2v5.gif"))
        self.__logger.log("Configurando botões e telas com métodos")
        self.lay = QtWidgets.QHBoxLayout(self.tela_sucesso.grafico)
        self.tela_inicial.enter_button.clicked.connect(self.set_choices)
        self.tela_inicial.login_button_s1.clicked.connect(self.valida_login)
        self.tela_inicial.login_button_ss.clicked.connect(self.valida_login)
        self.tela_inicial.voltar_s1.clicked.connect(self.show_page1)
        self.tela_inicial.voltar_ss.clicked.connect(self.show_page1)
        self.tela_inicial.olhar_senha_s1.clicked.connect(self.mostrar_senha)
        self.tela_inicial.olhar_senha_ss.clicked.connect(self.mostrar_senha)
        self.tela_inicial.cpf_s1.setInputMask("999.999.999-99")
        self.tela_carregamento.gif.setMovie(self.gif_loading)
        self.tela_principal.actionDesenvolvedores.triggered.connect(
            self.desenvolvedores
        )
        self.tela_desenvolvedores.return_button.clicked.connect(
            self.close_desenvolvedores
        )
        self.tela_principal.actionDocumentacao.triggered.connect(self.documentacao)
        self.tela_documentacao.return_button.clicked.connect(self.close_documentacao)
        self.tela_principal.next_button.clicked.connect(self.filtro_tipo_processo)
        self.tela_principal.start_button.clicked.connect(self.filtro_configuracoes)
        self.tela_sucesso.return_button.clicked.connect(self.fechando_tela_sucesso)
        self.tela_sucesso.gerar_relatorio.clicked.connect(self.gerar_relatorio)
        self.tela_principal.return_Button.clicked.connect(self.retorna_tela_principal)
        self.tela_inicial.setWindowTitle("Preferências")
        self.tela_desenvolvedores.setWindowTitle("Desenvolvedores")
        self.tela_sucesso.setWindowTitle("Concluido")
        self.tela_principal.setWindowTitle("Preferências")
        self.tela_carregamento.setWindowTitle("Carregando intimações e distribuindo")
        self.__logger.log("Ajustando ícones e imagens")
        icon_path = get_graphics_path("icon_r2d2.ico")
        self.tela_principal.setWindowIcon(QtGui.QIcon(icon_path))
        self.tela_inicial.setWindowIcon(QtGui.QIcon(icon_path))
        self.tela_desenvolvedores.setWindowIcon(QtGui.QIcon(icon_path))
        self.tela_sucesso.setWindowIcon(QtGui.QIcon(icon_path))
        self.tela_carregamento.setWindowIcon(QtGui.QIcon(icon_path))
        date_today = datetime.now()
        date = QDate(date_today.year, date_today.month, date_today.day)
        self.tela_principal.dateEdit_inicio.setDate(date)
        self.tela_principal.dateEdit_fim.setDate(date)
        self.tela_principal.frame_4.setVisible(False)
        self.tela_principal.distribuir_periodo.clicked.connect(self.mostrar_opcoes_data)
        self.tela_principal.distribuir_todos.clicked.connect(self.mostrar_opcoes_data)

    def verificar_memoria(self):
        mem = psutil.virtual_memory()
        if mem.percent > 90:
            self.__logger.log("AVISO: Uso de memória está acima de 90%")
        else:
            self.__logger.log(f"AVISO: Uso de memória está em {mem.percent}%")

    def __zerando_variaveis_valiacao(self):
        self.__logger.log("Zerando variáveis e textos das telas")
        self.tela_principal.determinar_processo.setText("")
        self.tela_principal.mensagem_erro.setText("")
        self.tela_principal.stackedWidget.setCurrentIndex(0)
        self.todos_processos = False
        self.processos_nao_integrados = False
        self.processos_integrados = False
        self.distribuir_etiquetar = False
        self.nao_distribuir_nao_etiquetar = False

    def __check_email(self, email: str) -> str:
        if "@agu.gov.br" not in email:
            self.tela_inicial.dados_incorretos_ss.setText("Digite seu email da AGU!")
            email = ""

        return email

    def check_choices(self) -> bool:
        retorno = False
        if not self.__sapiens_info["VERSAO"] and not self.__regiao:
            self.tela_inicial.erro_escolha_distribuicao.setText(
                "Escolha a Distribuição!"
            )
            self.tela_inicial.erro_escolha_regiao.setText("Escolha a Região!")
        elif not self.__sapiens_info["VERSAO"] or not self.__regiao:
            if not self.__sapiens_info["VERSAO"]:
                self.tela_inicial.erro_escolha_distribuicao.setText(
                    "Escolha a Distribuição!"
                )
                self.tela_inicial.erro_escolha_regiao.setText("")
            elif not self.__regiao:
                self.tela_inicial.erro_escolha_distribuicao.setText("")
                self.tela_inicial.erro_escolha_regiao.setText("Escolha a Região!")
        else:
            retorno = True

        return retorno

    def set_choices(self) -> None:
        self.tela_inicial.erro_escolha_distribuicao.setText("")
        self.__logger.log("Verificando opcoes escolhidas")

        if self.tela_inicial.radioButton_super_sapiens.isChecked():
            self.__sapiens_info["VERSAO"] = "SUPER"
        self.__logger.log(
            f"Usuário optou por utilizar o {self.__sapiens_info['VERSAO']}"
        )
        self.tela_inicial.cpf_s1.setText("")
        self.tela_inicial.senha_s1.setText("")
        self.tela_inicial.dados_incorretos_s1.setText("")
        self.tela_inicial.email.setText(EMAIL_SUPER)
        self.tela_inicial.senha_ss.setText(SENHA_SUPER)
        self.tela_inicial.dados_incorretos_ss.setText("")
        self.distribuidor.sapiens_info = self.__sapiens_info

        if self.tela_inicial.radioButton_pgu.isChecked():
            self.__regiao = "PGU"

        self.__logger.log(f"Usuario optou por trabalhar na região {self.__regiao}")

        if self.check_choices():
            self.tela_inicial.setWindowTitle("Login")
            self.show_page_tela_inicial()

    def definir_logo(self):
        if self.__regiao == "PGU":
            pixmap = QPixmap(get_graphics_path("Logo_PGU.png"))

        if self.__sapiens_info["VERSAO"] == "SUPER":
            self.tela_inicial.label_24.setPixmap(pixmap)

        self.tela_principal.label_10.setPixmap(pixmap)
        self.tela_principal.label_13.setPixmap(pixmap)

    def show_page_tela_inicial(self):
        if self.__sapiens_info["VERSAO"] == "SUPER":
            self.tela_inicial.stackedWidget.setCurrentWidget(
                self.tela_inicial.page3_login_super_sapiens
            )
        elif self.__sapiens_info["VERSAO"] == "SAPIENS":
            self.tela_inicial.stackedWidget.setCurrentWidget(
                self.tela_inicial.page2_login_sapiens1
            )
        self.definir_logo()

    def show_page1(self):
        self.tela_inicial.setWindowTitle("Preferências")
        self.tela_inicial.erro_escolha_distribuicao.setText("")
        self.tela_inicial.erro_escolha_regiao.setText("")
        self.tela_inicial.stackedWidget.setCurrentWidget(
            self.tela_inicial.page1_escolhas
        )

    def valida_login(self):
        retorno_sapiens = {}

        if self.__sapiens_info["VERSAO"] == "SAPIENS":
            cpf_nao_formatado = self.tela_inicial.cpf_s1.text()
            self.__usuario = re.sub("[..-]", "", cpf_nao_formatado)
            senha = self.tela_inicial.senha_s1.text()
            retorno_sapiens = self.distribuidor.sapiens.login(self.__usuario, senha)
            self.__logger.log(f"Logando no SAPIENS com usuário {self.__usuario}")

        if self.__sapiens_info["VERSAO"] == "SUPER":
            self.__usuario = self.__check_email(self.tela_inicial.email.text())
            senha = self.tela_inicial.senha_ss.text()
            retorno_sapiens = self.distribuidor.super_sapiens.login(
                self.__usuario, senha
            )

        if retorno_sapiens:
            self.__logger.log("Login realizado com sucesso")
            self.__sapiens_info["NOME_USUARIO"] = retorno_sapiens["NOME"]
            self.__sapiens_info["UNIDADES_USUARIO"] = retorno_sapiens["UNIDADES"]
            self.__logger.log(f"Nome do usuário: {self.__sapiens_info['NOME_USUARIO']}")
            self.__logger.log(
                f"Unidade do usuário: {self.__sapiens_info['UNIDADES_USUARIO']}"
            )
            self.tela_inicial.close()
            self.formatar_tela_principal()
        else:
            if self.__sapiens_info["VERSAO"] == "SUPER":
                self.tela_inicial.dados_incorretos_ss.setText("Dados Incorretos!")
            elif self.__sapiens_info["VERSAO"] == "SAPIENS":
                self.tela_inicial.dados_incorretos_s1.setText("Dados Incorretos!")

    def mostrar_senha(self):
        if self.__sapiens_info["VERSAO"] == "SUPER":
            if self.tela_inicial.olhar_senha_ss.isChecked():
                self.tela_inicial.olhar_senha_ss.setIcon(
                    QIcon(get_graphics_path("eye_hidden.png"))
                )
                self.tela_inicial.senha_ss.setEchoMode(QtWidgets.QLineEdit.Normal)
            else:
                self.tela_inicial.olhar_senha_ss.setIcon(
                    QIcon(get_graphics_path("eye_visible.png"))
                )
                self.tela_inicial.senha_ss.setEchoMode(QtWidgets.QLineEdit.Password)

    def formatar_tela_principal(self):
        self.tela_principal.show()

        if (
            self.__sapiens_info["NOME_USUARIO"]
            and self.__sapiens_info["UNIDADES_USUARIO"]
        ):
            regras_pgu = [
                self.__regiao == "PGU",
                "17" in self.__sapiens_info["UNIDADES_USUARIO"],
            ]
            if all(regras_pgu):
                self.__sapiens_info["UNIDADES_USUARIO"] = "17"
                self.tela_principal.nome_user_2.setText(
                    f"Bem vindo(a): {self.__sapiens_info['NOME_USUARIO']}"
                )
            else:
                self.__sapiens_info["NOME_USUARIO"] = ""
                self.__sapiens_info["UNIDADES_USUARIO"] = ""
                self.tela_principal.close()
                self.tela_inicial.show()
        else:
            if self.__sapiens_info["VERSAO"] == "SUPER":
                self.tela_inicial.dados_incorretos_ss.setText(
                    "Não foi possível fazer o login!"
                )
            self.__sapiens_info["NOME_USUARIO"] = ""
            self.__sapiens_info["UNIDADES_USUARIO"] = ""
            self.tela_principal.close()
            self.tela_inicial.show()

    def filtro_tipo_processo(self):
        if self.tela_principal.processos_integrados.isChecked():
            self.processos_integrados = True
        if self.tela_principal.processos_nao_integrados.isChecked():
            self.processos_nao_integrados = True
        if self.tela_principal.todos_processos.isChecked():
            self.todos_processos = True

        self.ler_aba_intimacoes(True)
        self.verifica_filtros()

    def filtro_configuracoes(self):
        if self.tela_principal.distribuir_etiquetar.isChecked():
            self.distribuir_etiquetar = True
        elif self.tela_principal.nao_distribuir_nao_etiquetar.isChecked():
            self.nao_distribuir_nao_etiquetar = True
        if self.tela_principal.distribuir_todos.isChecked():
            self.distribuir_todos = True
        elif self.tela_principal.distribuir_periodo.isChecked():
            self.distribuir_periodo = True
            self.captura_variaveis_filtro_data()
        self.verifica_filtro_configuracoes()

    # metodo que retorna as variáveis de data, para distribuição por data.
    def captura_variaveis_filtro_data(self):
        self.__periodo["DE"] = (
            datetime.strptime(self.tela_principal.dateEdit_inicio.text(), "%d/%m/%Y")
        ).strftime("%Y-%m-%d")

        self.__periodo["ATE"] = (
            datetime.strptime(self.tela_principal.dateEdit_fim.text(), "%d/%m/%Y")
        ).strftime("%Y-%m-%d")

    def mostrar_opcoes_data(self):
        if self.tela_principal.distribuir_periodo.isChecked():
            self.tela_principal.frame_4.setVisible(True)
        else:
            self.tela_principal.frame_4.setVisible(False)

    def verifica_filtro_configuracoes(self):
        regras = [
            not self.distribuir_etiquetar,
            not self.nao_distribuir_nao_etiquetar,
            not self.distribuir_todos,
            not self.distribuir_periodo,
        ]
        if all(regras):
            self.tela_principal.mensagem_erro.setText(
                "Escolha as opções de configuração!"
            )
        elif not self.distribuir_etiquetar and not self.nao_distribuir_nao_etiquetar:
            self.tela_principal.mensagem_erro.setText(
                "Escolha como deseja executar o Robô!"
            )
        elif not self.distribuir_todos and not self.distribuir_periodo:
            self.tela_principal.mensagem_erro.setText(
                "Selecione quais processos deseja distribuir."
            )
        else:
            self.inicia_gif()
            self.chamar_tela_carregamento()
            self.iniciando_aplicacao()

    def verifica_filtros(self):
        regras = [
            not self.processos_integrados,
            not self.processos_nao_integrados,
            not self.todos_processos,
        ]
        if all(regras):
            self.tela_principal.determinar_processo.setText(
                "Por favor, defina o tipo de processo!"
            )
        else:
            self.tela_principal.label_qtd_processos.setText(
                f"(Identifiquei {str(self.distribuidor.total)} intimações em seu painel)"
            )
            self.tela_principal.stackedWidget.setCurrentWidget(
                self.tela_principal.page02
            )
            self.tela_principal.nome_user.setText(
                f"Bem vindo(a): {self.__sapiens_info['NOME_USUARIO']}"
            )

    def iniciando_aplicacao(self):
        try:
            self.leituraIntimacoes_finalizado = False
            self.tela_carregamento.progressBar.setValue(0)
            self.tela_carregamento.progressBar.setMinimum(0)
            self.tela_carregamento.progressBar.setMaximum(self.distribuidor.total)
            self.tela_carregamento.progressBar.show()
            QCoreApplication.processEvents()
            if self.__sapiens_info["VERSAO"] == "SUPER":
                self.tela_carregamento.lb_Status.setText(
                    "Lendo Intimações do Painel a partir do Super Sapiens "
                )
            QCoreApplication.processEvents()
            self.thread_leitura_intimacoes()
            while not self.leituraIntimacoes_finalizado:
                QCoreApplication.processEvents()
        except Exception as e:
            self.__logger.log("-----------------")
            self.__logger.log(
                "Falha na configuração da janela de carregamento e status da análise"
            )
            self.__logger.log(f"Exceção levantada foi: {e}")
            self.__logger.log("-----------------")

    def ler_aba_intimacoes(self, total: bool = False):
        """
        Função responsável por ler a aba intimações de acordo com o filtro do tipo de processo
        que o usúario deseja ler
        """
        QCoreApplication.processEvents()
        if self.todos_processos:
            self.distribuidor.ler_intimacoes(
                self.__sapiens_info["UNIDADES_USUARIO"], self.__periodo, total
            )
        if self.processos_integrados:
            self.distribuidor.ler_intimacoes(
                self.__sapiens_info["UNIDADES_USUARIO"], self.__periodo, total, True
            )
        if self.processos_nao_integrados:
            self.distribuidor.ler_intimacoes(
                self.__sapiens_info["UNIDADES_USUARIO"], self.__periodo, total, False
            )
        return "de Leitura de Intimações foi finalizado"

    def organizar_grafico_distribuicao(self):
        """
        Função que organiza o gráfico gerado ao final da execução da distribuição
        """
        self.series.clear()
        self.chart.removeSeries(self.series)
        self.create_donutchart()
        self.series.setHoleSize(0.35)
        self.distribuidos.setExploded()
        self.distribuidos.setLabelVisible()
        self.erro_statusintegracao.setLabelVisible()
        self.nao_distribuidos.setLabelVisible()
        self.chart.addSeries(self.series)
        self.chart.setAnimationOptions(QChart.SeriesAnimations)
        self.chart.setTheme(QChart.ChartThemeLight)
        self.chartview.setRenderHint(self.qpainter.Antialiasing)
        self.lay.addWidget(self.chartview)

    def create_donutchart(self):
        """
        Função responsável por guardar as variáveis que serão utilizadas no gráfico e tambem já cria o gráfico vazio
        """
        contador_processos = self.distribuidor.calcular_sugestoes_e_distribuicoes()

        self.distribuidos = self.series.append(
            str(f"Distribuidos: {contador_processos['distribuidos']}"),
            contador_processos["distribuidos"],
        )

        self.nao_distribuidos = self.series.append(
            str(f"Não Distribuidos: {contador_processos['nao_distribuidos']}"),
            contador_processos["nao_distribuidos"],
        )

        self.erro_statusintegracao = self.series.append(
            str(f"Falhas na integração: {contador_processos['falha_integracao']}"),
            contador_processos["falha_integracao"],
        )

    def thread_leitura_intimacoes_finalizada(self):
        self.leituraIntimacoes_finalizado = True
        self.__logger.log("Thread de leitura de intimações finalizada")

        self.tela_carregamento.lb_Status.setText("Distribuindo Intimações")
        QCoreApplication.processEvents()
        self.analisar_intimacoes()
        self.organizar_grafico_distribuicao()
        self.tela_carregamento.close()
        self.tela_sucesso.show()

        # LOCAL PARA INSERÇÃO DO RELATORIO E DA ESTATISTICA
        try:
            # obtendo a pasta do executável
            if getattr(sys, "frozen", False):
                folder = os.path.dirname(sys.executable)
            else:
                folder = os.path.dirname(
                    os.path.abspath(sys.modules["__main__"].__file__)
                )
            self.__logger.log("Criando relatorio das distribuições ")
            # obtendo instante atual para preparar o nome
            data_h = datetime.now()
            # montando o nome do relatorio
            "Alteraro por Fernando PRU6 para mês, dia hora e minuto com 2 casas decimais"
            # relatorio = f'Relatório_{data_h.year}_{data_h.month}_{data_h.day}__{data_h.hour}h{data_h.minute}min.xlsx'

            relatorio = f"Relatório_{data_h.year}_{data_h.month:0>2}_{data_h.day:0>2}__{data_h.hour:0>2}h{data_h.minute:0>2}min.xlsx"

            self.__logger.log(
                f"Criando relatorio das distribuições no arquivo {relatorio} "
            )
            # colocando nome para a pasta de Relatórios do atual executável
            folder = os.path.join(folder, "reports")
            if not os.path.exists(folder):
                os.makedirs(folder)
            relatorio = os.path.join(folder, relatorio)
            self.distribuidor.criar_relatorio(relatorio)
            self.__logger.log(f"Criado relatorio em {relatorio} ")
            self.__logger.log("Atualizando as estatísticas da execução")
            self.distribuidor.calcular_estatisticas_de_uso()
            self.__logger.log("Estatisticas da execução foram atualizadas")
        except Exception as e:
            self.__logger.log("Ocorreu um erro ao gerar o relatório ou estatística")
            self.__logger.log(f"Erro foi {e}")

        # zerando contadores opcoes do usuario
        self.__zerando_variaveis_valiacao()

    def thread_output(self, s: str):
        """
        Função para pegar o retorno do fim da Thread
        :param s Mensagem passada pelo fim da thread
        """
        self.__logger.log(f"Thread {s}")

    def thread_leitura_intimacoes(self):
        worker = Trabalhador(self.ler_aba_intimacoes)
        worker.sinais.resultado.connect(self.thread_output)
        worker.sinais.finalizado.connect(self.thread_leitura_intimacoes_finalizada)
        self.threadpool.start(worker)

    def gerar_relatorio(self):
        try:
            data_h = datetime.now()
            relatorio = QtWidgets.QFileDialog.getSaveFileName(
                self.tela_sucesso,
                None,
                f"Relatório-{data_h.day}-{data_h.month}-{data_h.year}__{data_h.hour}h{data_h.minute}min.xlsx",
                "Excel (*.xlsx",
            )[0]
            self.distribuidor.criar_relatorio(relatorio)

        except ValueError:
            pass

    def chamar_tela_carregamento(self):
        self.tela_carregamento.show()

    def inicia_gif(self):
        self.tela_carregamento.gif.show()
        self.gif_loading.start()

    def analisar_intimacoes(self):  # MÉTODO MAIN
        lista_filtrada = []
        if len(lista_filtrada) == 0:
            lista_filtrada = self.distribuidor.resp_intimacoes
        self.distribuidor.total = len(lista_filtrada)
        self.tela_carregamento.progressBar.setMaximum(self.distribuidor.total)

        self.__logger.log(
            f"Total de intimações depois de filtradas =  {self.distribuidor.total} intimações"
        )
        for i, intimacao in enumerate(lista_filtrada):
            self.__logger.log("#" * 80)
            self.__logger.log(f"INTIMAÇÃO SEGUENCIAL = {i + 1}")
            self.__logger.log("#" * 80)

            QCoreApplication.processEvents()

            self.distribuidor.extrair_dados_intimacao(intimacao)

            if (
                self.distribuidor.i_intimacao["ID"]
                in self.distribuidor.id_processo_list
            ):
                pass

            else:
                # analisa e distribui a intimação
                if self.__regiao == "PGU":
                    self.__pgu.analisar_intimacao()
                    QCoreApplication.processEvents()

                if self.nao_distribuir_nao_etiquetar:
                    self.distribuidor.gravar_etiqueta(False)
                    self.distribuidor.distribuir_processo(False, False)
                else:
                    self.distribuidor.gravar_etiqueta()
                    time.sleep(self.__time_i)
                    self.distribuidor.distribuir_processo()
                    time.sleep(self.__time_i)

                # Dados Relatorio
                self.distribuidor.id_processo_list.append(
                    self.distribuidor.i_intimacao["ID"]
                )
                self.distribuidor.classe_processual_list.append(
                    self.distribuidor.i_intimacao["CLASSE_PROCESSUAL"]
                )
                self.distribuidor.num_jud_list.append(
                    self.distribuidor.i_intimacao["NUMERO"]
                )
                self.distribuidor.nup_list.append(self.distribuidor.i_intimacao["NUP"])
                self.distribuidor.assunto_list.append(
                    self.distribuidor.i_intimacao["ASSUNTOS"]
                )
                self.distribuidor.ult_setor_list.append(
                    self.distribuidor.i_intimacao["ULTIMO_SETOR"]
                )
                self.distribuidor.modalidade_list.append(
                    self.distribuidor.i_intimacao["MODALIDADE"]
                )
                # self.distribuidor.status_processo_list.append(
                #     self.distribuidor.status_processo["FLAG"]
                # )
                self.distribuidor.etiqueta_final_list.append(
                    self.distribuidor.i_intimacao["POSTIT"]
                )
                self.distribuidor.contador_relatorio_list.append(
                    self.distribuidor.contador_relatorio["FLAG"]
                )
                self.distribuidor.orgao_julgador_list.append(
                    self.distribuidor.i_intimacao["ORGAO_JULGADOR"]
                )
                self.distribuidor.teor_list.append(
                    self.distribuidor.i_intimacao["TEOR"]
                )
                self.distribuidor.lembrete_list.append(
                    self.distribuidor.lembrete_relatorio
                )
                self.distribuidor.fonte_dados_list.append(
                    self.distribuidor.i_intimacao["FONTE_DADOS"]
                )

            self.distribuidor.zerando_variaves()

            if i == (self.distribuidor.total - 1):
                break

    def fechando_tela_sucesso(self):
        self.__zerando_variaveis_valiacao()
        self.tela_sucesso.close()

    def desenvolvedores(self):
        self.tela_desenvolvedores.show()

    def close_desenvolvedores(self):
        self.tela_desenvolvedores.close()

    def documentacao(self):
        self.tela_documentacao.show()

    def close_documentacao(self):
        self.tela_documentacao.close()

    def retorna_tela_principal(self):
        self.__zerando_variaveis_valiacao()
        self.tela_principal.stackedWidget.setCurrentWidget(self.tela_principal.page01)


class Trabalhador(QRunnable):
    def __init__(self, metodo, *args, **kwargs):
        super(Trabalhador, self).__init__()
        self.metodo = metodo
        self.args = args
        self.kwargs = kwargs
        self.sinais = Sinais_Trabalhador()
        # metodo que vai informar o que está ocorrendo no processamento da leitura de intimações
        # self.kwargs['progresso_callback'] = self.sinais.progresso

    @pyqtSlot()
    def run(self):
        try:
            resultado = self.metodo(*self.args, **self.kwargs)
        except Exception as e:
            traceback.print_exc()
            exctype, value = sys.exc_info()[:2]
            self.sinais.erro((exctype, value, traceback.format_exc()))
            print(e)

        else:
            self.sinais.resultado.emit(resultado)

        finally:
            self.sinais.finalizado.emit()


class Sinais_Trabalhador(QObject):
    finalizado = pyqtSignal()
    erro = pyqtSignal(tuple)
    resultado = pyqtSignal(object)
    # progresso =  pyqtSignal(str)


if __name__ == "__main__":
    pass
