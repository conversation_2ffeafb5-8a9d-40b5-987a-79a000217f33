<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>430</width>
    <height>400</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>430</width>
    <height>400</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>430</width>
    <height>400</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:white;</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QProgressBar" name="progressBar">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>300</y>
      <width>401</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>11</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">QProgressBar{
	border-radius:8px;
	color:rgb(70,70,70);
	border: 2px solid rgb(170,170,170);
}

QProgressBar::chunk{
    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgb(85, 122, 242), stop:1 rgb(62, 91, 185));
border-radius:8px;
}</string>
    </property>
    <property name="value">
     <number>15</number>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="gif">
    <property name="geometry">
     <rect>
      <x>80</x>
      <y>50</y>
      <width>350</width>
      <height>225</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::NoFrame</enum>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="lb_Status">
    <property name="geometry">
     <rect>
      <x>40</x>
      <y>0</y>
      <width>361</width>
      <height>41</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>11</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color:white;</string>
    </property>
    <property name="text">
     <string>Distribuindo Processos...</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QFrame" name="frame">
    <property name="geometry">
     <rect>
      <x>350</x>
      <y>30</y>
      <width>81</width>
      <height>251</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
   </widget>
   <zorder>gif</zorder>
   <zorder>progressBar</zorder>
   <zorder>lb_Status</zorder>
   <zorder>frame</zorder>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>430</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
