import sys
import time

from PyQt5 import QtCore
from PyQt5.QtGui import QPixmap
from PyQt5.QtWidgets import QApplication, QSplashScreen

from services.qtdesigner import TelasDistribuidor
from utils.resource_path import get_graphics_path

app = QApplication(sys.argv)
r2d2 = TelasDistribuidor()
splash_object = QSplashScreen(
    QPixmap(get_graphics_path("R2D2_Splash_480.png")),
    QtCore.Qt.WindowStaysOnTopHint,
)
opaqueness = 0.0
step = 0.1
splash_object.setWindowOpacity(opaqueness)
splash_object.show()
while opaqueness < 1:
    splash_object.setWindowOpacity(opaqueness)
    time.sleep(step)
    opaqueness += step
time.sleep(2.0)
splash_object.close()
r2d2.tela_inicial.show()
app.exec()
