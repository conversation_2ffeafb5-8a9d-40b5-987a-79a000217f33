import base64
import json
import re
from datetime import datetime, timedelta
from typing import Optional

import requests
from PyQt5.QtWidgets import QApplication

from clients.payload import Payload
from services.logger import LogMessage
from services.pgu import SUPER_SAPIENS


class Super_Sapiens:
    def __init__(self, token: Optional[str] = None, logger: LogMessage = None) -> None:
        self.token = token
        self.expiration = None
        self.baseurl = SUPER_SAPIENS
        self.__limit = 500
        self.__offset = 0
        self.__logger = logger
        if token is not None:
            self.get_usuario()

    def url(self, url: Optional[str] = None):
        return re.sub(r"^/?(.*)$", f"{self.baseurl}/\\1", url or "")

    def prepare_params(self, **kwargs):
        headers = kwargs.pop("headers", {})
        self.__check_expiration()
        if self.token:
            authkey = next(
                (k for k in kwargs.keys() if k.casefold() == "authorization"),
                "Authorization",
            )
            headers[authkey] = f"Bearer {self.token}"
        params = kwargs.pop("params", {})
        self.__check_expiration()
        if "limit" in kwargs:
            params["limit"] = str(kwargs.pop("limit"))
        if "offset" in kwargs:
            params["offset"] = str(kwargs.pop("offset"))
        if "order" in kwargs:
            params["order"] = json.dumps(kwargs.pop("order"))
        if "context" in kwargs:
            params["context"] = json.dumps(kwargs.pop("context"))
        if "where" in kwargs:
            params["where"] = json.dumps(kwargs.pop("where"))
        if "populate" in kwargs:
            params["populate"] = json.dumps(kwargs.pop("populate"))
        kwargs.update(headers=headers, params=params)
        return kwargs

    def get(self, resource, **kwargs):
        return requests.get(url=self.url(resource), **self.prepare_params(**kwargs))

    def get_all(self, resource, **kwargs):
        limit = kwargs.get("limit", 25)
        total = limit
        fails = 0
        offset = 0
        while fails < 5 and total > offset:
            kwargs["offset"] = offset
            result = self.get(resource, **kwargs).json()
            if (
                result.get("total")
                or "entities" in result
                and isinstance(result["entities"], list)
            ):
                fails = 0
                total = result.get("total", len(result.get("entities", [])))
                return result
            else:
                fails += 1
            if fails >= 5:
                return result

            offset += limit

    def post(self, resource, body, **kwargs):
        jbody = body if isinstance(body, (dict, list)) else None
        body = body if jbody is None else None
        return requests.post(
            url=self.url(resource),
            data=body,
            json=jbody,
            **self.prepare_params(**kwargs),
        )

    def patch(self, resource, body, **kwargs):
        jbody = body if isinstance(body, (dict, list)) else None
        body = body if jbody is None else None
        return requests.patch(
            url=self.url(resource),
            data=body,
            json=jbody,
            **self.prepare_params(**kwargs),
        )

    def put(self, resource, body, **kwargs):
        jbody = body if isinstance(body, (dict, list)) else None
        body = body if jbody is None else None
        return requests.put(
            url=self.url(resource),
            data=body,
            json=jbody,
            **self.prepare_params(**kwargs),
        )

    def delete(self, resource, **kwargs):
        return requests.delete(url=self.url(resource), **self.prepare_params(**kwargs))

    def options(self, resource, **kwargs):
        return requests.options(url=self.url(resource), **self.prepare_params(**kwargs))

    def login(self, username: str, password: str) -> dict:
        retorno = {}
        self.token = None
        resp = self.post(
            "auth/ldap_get_token", {"username": username, "password": password}
        )
        result = {"success": False}

        try:
            resp_body = resp.json()
        except ValueError as value_error:
            resp_body = {"message": str(value_error)}

        result.update(resp_body)
        if "token" in resp_body:
            self.token = resp_body["token"]
            self.expiration = datetime.fromtimestamp(resp_body["exp"])
            result["success"] = True

        if result.get("success", False):
            retorno = self.get_usuario()

        self.payload = Payload(self.baseurl, username, password, self.token)

        return retorno

    def __refresh_token(self):
        headers = {"Authorization": f"Bearer {self.token}"}
        resp = requests.get(self.url("auth/refresh_token"), headers=headers)
        if resp.status_code == 200:
            body = resp.json()
            self.token = body.get("token", "")
            self.expiration = datetime.fromtimestamp(body["exp"])

    def __check_expiration(self):
        # Se não tem token, sai
        if not self.token:
            return
        # Se não tem expiração, pega a partir do token
        if not self.expiration:
            self.expiration = self.__get_expiration_from_token()

        # Se ainda não tem expiração, é porque não tem token
        if not self.expiration:
            return

        # Atualiza o token se for vencer nos próximos 5 minutos
        if self.expiration < datetime.now() + timedelta(minutes=5):
            self.__refresh_token()

        if self.expiration < datetime.now():
            raise f"Token expirado em {self.expiration.strftime('%d/%m/%Y %H:%M:%S')}, não é possível continuar"

    def __get_expiration_from_token(self):
        if not self.token:
            return
        parts = self.token.split(".")
        if len(parts) != 3:
            raise "Token inválido!"
        payload = parts[1]
        dados = json.loads(
            base64.b64decode(payload.encode("utf-8") + b"==").decode("utf-8")
        )
        return datetime.fromtimestamp(dados.get("exp", 0))

    def get_usuario(self) -> dict:
        retorno = {}
        unidade = []
        perfil = self.get("/profile").json()

        nome_usuario = perfil["nome"]
        split = nome_usuario.split(" ")
        nome_usuario_f = f"{split[0]} {split[len(split) - 1]}"
        retorno["NOME"] = nome_usuario_f.title()

        lotacoes = perfil["colaborador"]["lotacoes"]
        for i in range(len(lotacoes)):
            unidade.append(
                perfil["colaborador"]["lotacoes"][i]["setor"]["unidade"]["id"]
            )
        unidades = [i for n, i in enumerate(unidade) if i not in unidade[n + 1 :]]
        retorno["UNIDADES"] = re.sub("[^0-9,]", "", str(unidades).strip("-./; "))

        return retorno

    def get_comunicacao_judicial(self, parametros: dict) -> list:
        """
        - UNIDADES_IDS (obrigatório):str = 1 ou 1,2 ou 1,2,3 ou 1,2,3,4
        - INTEGRACAO (não obrigatório):bool = True ou False ou ""
        - PERIODO (não obrigatório):dict = {
          "DE": "2023-09-01",
          "ATE": "2023-09-07"
          } ou {}
        - TOTAL (não obrigatório):dict = True ou False ou ""
        """
        retorno = []
        intimacoes = []
        where_dict = {}
        andx_list = []

        # populate para trazer informações necessárias para analise
        populate = [
            "populateAll",
            "vinculacaoProcessoJudicialProcesso.processo",
            "vinculacaoProcessoJudicialProcesso.processo.interessados",
            "vinculacaoProcessoJudicialProcesso.processo.interessados.pessoa",
            "vinculacaoProcessoJudicialProcesso.processo.assuntos",
            "vinculacaoProcessoJudicialProcesso.processo.assuntos.assuntoAdministrativo",
            "vinculacaoProcessoJudicialProcesso.processo.vinculacoesEtiquetas",
            "vinculacaoProcessoJudicialProcesso.processo.vinculacoesEtiquetas.etiqueta",
            "vinculacaoProcessoJudicialProcesso.processo.lembretes",
            "processoJudicial.orgaoJulgador",
            "processoJudicial.orgaoJulgador.tribunal",
            "processoJudicial.classeNacional",
            "vinculacoesEtiquetasJudiciais",
            "vinculacoesEtiquetasJudiciais.etiqueta",
            "tarefa.usuarioResponsavel",
            "setor.unidade",
        ]

        if "UNIDADES_IDS" in parametros:
            if parametros["UNIDADES_IDS"]:
                where_dict["unidade.id"] = f"in:{parametros['UNIDADES_IDS']}"
                where_dict["tarefa"] = "isNull"
                if "INTEGRACAO" in parametros:
                    if parametros["INTEGRACAO"]:
                        andx_list.append(
                            {
                                "integracao": f"eq:{str(parametros['INTEGRACAO']).lower()}"
                            }
                        )
                if "PERIODO" in parametros:
                    if parametros["PERIODO"]:
                        andx_list.append(
                            {"criadoEm": f"gte:{parametros['PERIODO']['DE']}T00:00:00"}
                        )
                        andx_list.append(
                            {"criadoEm": f"lte:{parametros['PERIODO']['ATE']}T00:00:00"}
                        )
                if andx_list:
                    where_dict["andX"] = andx_list

                if "TOTAL" in parametros:
                    if parametros["TOTAL"]:
                        self.__logger.log("Buscando apenas o total de intimações")
                        intimacoes = self.get(
                            "/v1/judicial/comunicacao_judicial/count", where=where_dict
                        )
                        resp = json.loads(intimacoes.text)
                        self.__logger.log(f"Quantidade de intimações é: {resp}")

                        return [resp]

                intimacoes = self.get(
                    "/v1/judicial/comunicacao_judicial",
                    where=where_dict,
                    limit=self.__limit,
                    offset=self.__offset,
                    populate=populate,
                ).json()

                if "total" in intimacoes and "entities" in intimacoes:
                    if intimacoes["entities"]:
                        qtd = intimacoes["total"]
                        retorno = retorno + intimacoes["entities"]
                        if qtd > self.__limit:
                            for x in range(self.__limit, qtd, self.__limit):
                                intimacoes = self.get(
                                    "/v1/judicial/comunicacao_judicial",
                                    where=where_dict,
                                    limit=self.__limit,
                                    offset=x,
                                    populate=populate,
                                ).json()
                                if x + self.__limit < qtd:
                                    self.__logger.log(
                                        f"Intimações de {x} a {x + self.__limit} obtidas"
                                    )
                                else:
                                    self.__logger.log(
                                        f"Intimações de {x} a {qtd} obtidas"
                                    )
                                QApplication.processEvents()
                                if "entities" in intimacoes:
                                    if intimacoes["entities"]:
                                        retorno = retorno + intimacoes["entities"]
                                self.__logger.log("Intimações apensadas na lista")

        return retorno

    def create_tarefa(self, tarefa: dict) -> object:
        retorno = []

        payload = json.dumps(self.payload.create_tarefa_super(tarefa))
        resource = "/v1/administrativo/tarefa"

        populate = []
        context = {}
        context["intimacao"] = tarefa["comunicacaoJudicial_id"]
        context["migrar"] = True
        context["numero"] = tarefa["comunicacaoJucicial_numero"]

        try:
            retorno = self.post(
                resource=resource, body=payload, populate=populate, context=context
            )
        except Exception as e:
            self.__logger.log(f"Erro ao criar tarefa (post): {str(e)}")
            self.__logger.log(f"TAREFA: {tarefa}")
            self.__logger.log(f"PAYLOAD: {payload}")
            self.__logger.log(f"REQUISICAO_RESPOSTA: {retorno}")

        return retorno
