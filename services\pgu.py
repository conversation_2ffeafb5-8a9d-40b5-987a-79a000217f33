import pandas as pd
from decouple import Config, RepositoryEnv

from utils.resource_path import get_data_path, get_env_path

config = Config(RepositoryEnv(get_env_path()))

ENVIAR = "ENVIAR NORMALMENTE ULTIMO SETOR"

SUPER_SAPIENS = config("SUPER_SAPIENS")
ID_UNIDADE_PGU_SUPER = config("ID_UNIDADE_PGU_SUPER")
UNIDADES = {}
NAO_DISTRIBUIDO = config("NAO_DISTRIBUIDO")
SUGESTAO = config("SUGESTAO")
DISTRIBUIDO = config("DISTRIBUIDO")
ERRO_STATUSINTEGRACAO = config("ERRO_STATUSINTEGRACAO")
PGU = config("PGU")

PNPP = config("PNPP")
PNPRO = config("PNPRO")
PNSM = config("PNSM")
PNPP = config("PNPP")
PNTE = config("PNTE")
PNNE = config("PNNE")
NUAN = config("NUAN")
PNAI = config("PNAI")
NUGEP = config("NUGEP")
CONAPRO = config("CONAPRO")

PNPP_ID = config("PNPP_ID")
PNPRO_ID = config("PNPRO_ID")
PNSM_ID = config("PNSM_ID")
PNPP_ID = config("PNPP_ID")
PNTE_ID = config("PNTE_ID")
PNNE_ID = config("PNNE_ID")
NUAN_ID = config("NUAN_ID")
PNAI_ID = config("PNAI_ID")
NUGEP_ID = config("NUGEP_ID")
CONAPRO_ID = config("CONAPRO_ID")

PNPP_ID_PAUTA = config("PNPP_ID_PAUTA")
PNPRO_ID_PAUTA = config("PNPRO_ID_PAUTA")
PNSM_ID_PAUTA = config("PNSM_ID_PAUTA")
PNPP_ID_PAUTA = config("PNPP_ID_PAUTA")
PNTE_ID_PAUTA = config("PNTE_ID_PAUTA")
PNNE_ID_PAUTA = config("PNNE_ID_PAUTA")
NUAN_ID_PAUTA = config("NUAN_ID_PAUTA")
PNAI_ID_PAUTA = config("PNAI_ID_PAUTA")
NUGEP_ID_PAUTA = config("NUGEP_ID_PAUTA")
CONAPRO_ID_PAUTA = config("CONAPRO_ID_PAUTA")

VINCULO_SETOR_SETORID = {
    NUGEP: NUGEP_ID,  # DENTRO PNAI
    NUAN: NUAN_ID,  # DENTRO PNSM
    CONAPRO: CONAPRO_ID,  # DENTRO DA PNPRO
    PNPP: PNPP_ID,
    PNPRO: PNPRO_ID,
    PNSM: PNSM_ID,
    PNTE: PNTE_ID,
    PNNE: PNNE_ID,
    PNAI: PNAI_ID,
}  # SETORES ESPECÍFICOS COLOCAR EM PRIMEIRO NA LISTA

VINCULO_SETOR_SETORID_PAUTA = {
    NUGEP: NUGEP_ID_PAUTA,
    NUAN: NUAN_ID_PAUTA,
    CONAPRO: CONAPRO_ID_PAUTA,
    PNPP: PNPP_ID_PAUTA,
    PNPRO: PNPRO_ID_PAUTA,
    PNSM: PNSM_ID_PAUTA,
    PNTE: PNTE_ID_PAUTA,
    PNNE: PNNE_ID_PAUTA,
    PNAI: PNAI_ID_PAUTA,
}  # SETORES ESPECÍFICOS COLOCAR EM PRIMEIRO NA LISTA

LISTA_SETORES: list = [
    NUGEP,  # DENTRO PNAI
    NUAN,  # DENTRO PNSM
    CONAPRO,  # DENTRO DA PNPRO
    PNPP,
    PNPRO,
    PNSM,
    PNTE,
    PNNE,
    PNAI,
]  # SETORES ESPECÍFICOS COLOCAR EM PRIMEIRO NA LISTA


class Regras_PGU:
    def __init__(self, distribuidor):
        self.__distribuidor = distribuidor

    def __formatar_etiqueta(self, etiqueta: str) -> None:
        if self.__distribuidor.i_intimacao["POSTIT"]:
            if etiqueta in self.__distribuidor.i_intimacao["POSTIT"]:
                etiqueta_sem_formatacao = self.__distribuidor.i_intimacao["POSTIT"]
                etiqueta_sem_formatacao.replace(etiqueta, etiqueta)
                self.__distribuidor.i_intimacao["POSTIT"] = etiqueta_sem_formatacao
            else:
                self.__distribuidor.i_intimacao["POSTIT"] = (
                    f"{self.__distribuidor.i_intimacao['POSTIT']} | {etiqueta}"
                )
        else:
            self.__distribuidor.i_intimacao["POSTIT"] = etiqueta

    def __definir_setor_destino(self) -> None:
        self.__analise_dicionario_intimacoes_e_outros()

    def __verifica_processo_quando_nao_pode_usar_ultimo_setor(self) -> None:
        if self.__distribuidor.i_intimacao["NUP"]:
            if self.__distribuidor.i_intimacao["CLASSE_PROCESSUAL"]:
                self.__verifica_classe_processual()
            else:
                self.__formatar_etiqueta(f"R2D2: {self.__sugerir_etiqueta()}")
        else:
            self.__formatar_etiqueta(f"R2D2: {self.__sugerir_etiqueta()}")

    def __verifica_classe_processual(self):
        if self.__distribuidor.i_intimacao["ULTIMO_SETOR"]:
            return ENVIAR
        else:
            self.__formatar_etiqueta(f"R2D2: {self.__sugerir_etiqueta()}")

    def __analise_dicionario_intimacoes_e_outros(self) -> None:
        if self.__verificar_ultimo_setor() == ENVIAR:
            vinculo_setor_setorid_dict = VINCULO_SETOR_SETORID
            vinculo_setor_setorid_pauta_dict = VINCULO_SETOR_SETORID_PAUTA

            contador = 0

            if self.__distribuidor.i_intimacao["MODALIDADE"] == "PAUTA DE JULGAMENTO":
                for chave, valor in vinculo_setor_setorid_pauta_dict.items():
                    if chave in self.__distribuidor.i_intimacao["ULTIMO_SETOR"]:
                        self.__distribuidor.i_intimacao["SETOR_ID"] = valor
                        if self.__distribuidor.i_intimacao["SETOR_ID"]:
                            self.__distribuidor.contador_relatorio["FLAG"] = DISTRIBUIDO
                            self.__distribuidor.i_intimacao["POSTIT"] = (
                                f"R2D2: U_SETOR_PAUTA - {chave}"
                            )
                            contador += 1
                        break
            elif (
                self.__distribuidor.i_intimacao["MODALIDADE"] == "INTIMAÇÃO"
                or self.__distribuidor.i_intimacao["MODALIDADE"] == "CITAÇÃO"
            ):
                for chave, valor in vinculo_setor_setorid_dict.items():
                    if chave in self.__distribuidor.i_intimacao["ULTIMO_SETOR"]:
                        self.__distribuidor.i_intimacao["SETOR_ID"] = valor
                        if self.__distribuidor.i_intimacao["SETOR_ID"]:
                            self.__distribuidor.contador_relatorio["FLAG"] = DISTRIBUIDO
                            self.__distribuidor.i_intimacao["POSTIT"] = (
                                f"R2D2: U_SETOR - {chave}"
                            )
                            contador += 1
                        break
            if contador == 0:
                self.__distribuidor.i_intimacao["SETOR_ID"] = ""
                self.__verifica_processo_quando_nao_pode_usar_ultimo_setor()

    def __verificar_ultimo_setor(self) -> str:
        x = 0
        lista_ultimo_setor = LISTA_SETORES
        for valor in lista_ultimo_setor:
            if valor in self.__distribuidor.i_intimacao["ULTIMO_SETOR"]:
                x += 1
        if x > 0:
            return ENVIAR
        else:
            # if self.__analisa_lembrete() != "ENVIO ULTIMO SETOR":
            if self.__distribuidor.i_intimacao["CLASSE_PROCESSUAL"]:
                if self.__verifica_classe_processual() == ENVIAR:
                    return ENVIAR
            else:
                return ENVIAR
        return ""

    def __sugerir_etiqueta(self) -> str:
        if len(self.__distribuidor.i_intimacao["ASSUNTOS"]) == 0:
            self.__distribuidor.status_processo["FLAG"] = NAO_DISTRIBUIDO
            self.__distribuidor.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO
            return "SEM SUGESTÃO"
        else:
            tabela = pd.read_excel(
                get_data_path("tabela_sugestao_etiqueta.xlsx"),
                dtype=str,
            )
            for i in tabela.index:
                assuntoTabela = tabela["ASSUNTO EXATO"][i]
                etiqueta = tabela["COORDENAÇÃO CORRESPONDENTE"][i]
                for assunto in self.__distribuidor.i_intimacao[
                    "ASSUNTOS"
                ]:  # APENAS ASSUNTO PRINCIPAL
                    if assunto["PRINCIPAL"] and assuntoTabela == assunto["NOME"]:
                        self.__distribuidor.status_processo["FLAG"] = SUGESTAO
                        self.__distribuidor.contador_relatorio["FLAG"] = SUGESTAO
                        return f"SUGESTÃO - TABELA FREQUENCIA: {etiqueta}"
            self.__distribuidor.status_processo["FLAG"] = NAO_DISTRIBUIDO
            self.__distribuidor.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO
            return "SEM SUGESTÃO"

    def analisar_intimacao(self) -> None:
        if self.__distribuidor.i_intimacao["ULTIMO_SETOR"]:  # PROCESSO EM CURSO
            self.__definir_setor_destino()
        else:
            self.__verifica_processo_quando_nao_pode_usar_ultimo_setor()  # PROCESSO NOVO


if __name__ == "__main__":
    pass
