<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QFrame" name="frame">
      <property name="minimumSize">
       <size>
        <width>460</width>
        <height>380</height>
       </size>
      </property>
      <property name="autoFillBackground">
       <bool>false</bool>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: white;
border-radius: 10px;</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <property name="lineWidth">
       <number>0</number>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QStackedWidget" name="stackedWidget">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>500</width>
           <height>600</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>500</width>
           <height>600</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background:white;</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="page01">
          <widget class="QLabel" name="nome_user_2">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>10</y>
             <width>411</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Name</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_12">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>520</y>
             <width>71</width>
             <height>71</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>r2d2_preto.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_13">
           <property name="geometry">
            <rect>
             <x>195</x>
             <y>555</y>
             <width>124</width>
             <height>41</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Logo_PGU.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
           <property name="wordWrap">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_4">
           <property name="geometry">
            <rect>
             <x>210</x>
             <y>260</y>
             <width>251</width>
             <height>171</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>ImgComputador.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_7">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>80</y>
             <width>441</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Qual tipo de processo deseja distribuir:</string>
           </property>
          </widget>
          <widget class="QRadioButton" name="todos_processos">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>250</y>
             <width>241</width>
             <height>17</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>80</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="text">
            <string>[TODOS] os Processos.</string>
           </property>
          </widget>
          <widget class="QRadioButton" name="processos_integrados">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>195</y>
             <width>261</width>
             <height>17</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="text">
            <string>Processos Integrados.</string>
           </property>
          </widget>
          <widget class="QRadioButton" name="processos_nao_integrados">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>140</y>
             <width>351</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="text">
            <string>Processos [NAO] Integrados.</string>
           </property>
          </widget>
          <widget class="QLabel" name="determinar_processo">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>430</y>
             <width>321</width>
             <height>41</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:red;</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QPushButton" name="next_button">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>490</y>
             <width>291</width>
             <height>51</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
    color:white;
	background-color: rgb(62, 91, 185);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(75, 107, 209);
}
QPushButton:pressed{
    background-color: rgb(36, 81, 224);
    border: 2px solid rgb(231, 231, 231);
}</string>
           </property>
           <property name="text">
            <string>Proximo</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_5">
           <property name="geometry">
            <rect>
             <x>360</x>
             <y>160</y>
             <width>91</width>
             <height>91</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>pasta.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_8">
           <property name="geometry">
            <rect>
             <x>430</x>
             <y>530</y>
             <width>61</width>
             <height>61</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>python.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </widget>
         <widget class="QWidget" name="page02">
          <widget class="QPushButton" name="start_button">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>450</y>
             <width>311</width>
             <height>41</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
    color:white;
	background-color: rgb(62, 91, 185);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(75, 107, 209);
}
QPushButton:pressed{
    background-color: rgb(36, 81, 224);
    border: 2px solid rgb(231, 231, 231);
}</string>
           </property>
           <property name="text">
            <string>Iniciar Automação</string>
           </property>
          </widget>
          <widget class="QLabel" name="nome_user">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>-1</y>
             <width>411</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Name</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_3">
           <property name="geometry">
            <rect>
             <x>328</x>
             <y>245</y>
             <width>141</width>
             <height>181</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>R22D2.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_10">
           <property name="geometry">
            <rect>
             <x>195</x>
             <y>555</y>
             <width>124</width>
             <height>41</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>logoPGU.jpg</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
           <property name="wordWrap">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_11">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>520</y>
             <width>71</width>
             <height>71</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>r2d2_preto.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="mensagem_erro">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>370</y>
             <width>311</width>
             <height>61</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:red;</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_qtd_processos">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>35</y>
             <width>411</width>
             <height>20</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Voce tem tantos processos</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QPushButton" name="return_Button">
           <property name="geometry">
            <rect>
             <x>110</x>
             <y>510</y>
             <width>291</width>
             <height>41</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
    color:white;
	background-color: rgb(62, 91, 185);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(75, 107, 209);
}
QPushButton:pressed{
    background-color: rgb(36, 81, 224);
    border: 2px solid rgb(231, 231, 231);
}</string>
           </property>
           <property name="text">
            <string>Retornar</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_6">
           <property name="geometry">
            <rect>
             <x>430</x>
             <y>530</y>
             <width>61</width>
             <height>61</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>python.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QFrame" name="frame_2">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>60</y>
             <width>451</width>
             <height>101</height>
            </rect>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <widget class="QRadioButton" name="distribuir_etiquetar">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>50</y>
              <width>400</width>
              <height>20</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string>Distribuir com etiquetamento [PADRÃO].</string>
            </property>
           </widget>
           <widget class="QRadioButton" name="nao_distribuir_nao_etiquetar">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>80</y>
              <width>350</width>
              <height>20</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string>Gerar apenas relatório - Excel.</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_9">
            <property name="geometry">
             <rect>
              <x>20</x>
              <y>15</y>
              <width>441</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Escolha como deseja executar esse robô:</string>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
          </widget>
          <widget class="QFrame" name="frame_3">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>170</y>
             <width>451</width>
             <height>181</height>
            </rect>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <widget class="QLabel" name="label_14">
            <property name="geometry">
             <rect>
              <x>20</x>
              <y>20</y>
              <width>441</width>
              <height>20</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Deseja definir um período?</string>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QRadioButton" name="distribuir_todos">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>50</y>
              <width>260</width>
              <height>20</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string>[NÃO] Todos os processos.</string>
            </property>
           </widget>
           <widget class="QRadioButton" name="distribuir_periodo">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>80</y>
              <width>170</width>
              <height>20</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string>Definir período:</string>
            </property>
           </widget>
           <widget class="QFrame" name="frame_4">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="geometry">
             <rect>
              <x>50</x>
              <y>100</y>
              <width>181</width>
              <height>80</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <kerning>true</kerning>
             </font>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <widget class="QDateEdit" name="dateEdit_fim">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>40</y>
               <width>110</width>
               <height>22</height>
              </rect>
              </property>
             <property name="styleSheet">
              <string notr="true">color:rgb(0, 0, 0)
</string>
             </property>
             <property name="maximumTime">
              <time>
               <hour>23</hour>
               <minute>59</minute>
               <second>59</second>
              </time>
             </property>
             <property name="minimumTime">
              <time>
               <hour>0</hour>
               <minute>0</minute>
               <second>0</second>
              </time>
             </property>
             <property name="calendarPopup">
              <bool>true</bool>
             </property>
             <property name="date">
              <date>
               <year>2023</year>
               <month>1</month>
               <day>1</day>
              </date>
             </property>
            </widget>
            <widget class="QLabel" name="label_16">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>34</y>
               <width>31</width>
               <height>31</height>
              </rect>
             </property>
             <property name="font">
              <font>
               <pointsize>11</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Até:</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
            <widget class="QLabel" name="label_15">
             <property name="geometry">
              <rect>
               <x>9</x>
               <y>4</y>
               <width>31</width>
               <height>31</height>
              </rect>
             </property>
             <property name="font">
              <font>
               <pointsize>11</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>De:</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
            <widget class="QDateEdit" name="dateEdit_inicio">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>10</y>
               <width>110</width>
               <height>22</height>
              </rect>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="acceptDrops">
              <bool>false</bool>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="wrapping">
              <bool>false</bool>
             </property>
             <property name="readOnly">
              <bool>false</bool>
             </property>
             <property name="buttonSymbols">
              <enum>QAbstractSpinBox::UpDownArrows</enum>
             </property>
             <property name="accelerated">
              <bool>false</bool>
             </property>
             <property name="keyboardTracking">
              <bool>false</bool>
             </property>
             <property name="showGroupSeparator" stdset="0">
              <bool>false</bool>
             </property>
             <property name="dateTime">
              <datetime>
               <hour>0</hour>
               <minute>0</minute>
               <second>0</second>
               <year>2023</year>
               <month>1</month>
               <day>1</day>
              </datetime>
             </property>
             <property name="minimumDate">
              <date>
               <year>2020</year>
               <month>9</month>
               <day>14</day>
              </date>
             </property>
             <property name="maximumTime">
              <time>
               <hour>23</hour>
               <minute>59</minute>
               <second>59</second>
              </time>
             </property>
             <property name="minimumTime">
              <time>
               <hour>0</hour>
               <minute>0</minute>
               <second>0</second>
              </time>
             </property>
             <property name="currentSection">
              <enum>QDateTimeEdit::MonthSection</enum>
             </property>
             <property name="displayFormat">
              <string>dd/MM/yyyy</string>
             </property>
             <property name="calendarPopup">
              <bool>true</bool>
             </property>
             <property name="currentSectionIndex">
              <number>1</number>
             </property>
             <property name="timeSpec">
              <enum>Qt::TimeZone</enum>
             </property>
             <property name="date">
              <date>
               <year>2023</year>
               <month>1</month>
               <day>1</day>
              </date>
             </property>
            </widget>
           </widget>
          </widget>
          <zorder>frame_3</zorder>
          <zorder>start_button</zorder>
          <zorder>nome_user</zorder>
          <zorder>label_10</zorder>
          <zorder>label_11</zorder>
          <zorder>mensagem_erro</zorder>
          <zorder>label_qtd_processos</zorder>
          <zorder>return_Button</zorder>
          <zorder>label_6</zorder>
          <zorder>frame_2</zorder>
          <zorder>label_3</zorder>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>600</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuDesenvolvedores">
    <property name="title">
     <string>Informacoes</string>
    </property>
    <addaction name="actionDesenvolvedores"/>
    <addaction name="actionDocumentacao"/>
   </widget>
   <addaction name="menuDesenvolvedores"/>
  </widget>
  <action name="actionDesenvolvedores">
   <property name="text">
    <string>Desenvolvedores</string>
   </property>
  </action>
  <action name="actionDocumentacao">
   <property name="text">
    <string>Documentacao</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="resources/images/images.qrc"/>
  <include location="resources/images/images.qrc"/>
 </resources>
 <connections/>
</ui>
