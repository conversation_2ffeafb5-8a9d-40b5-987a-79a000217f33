<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>tela_login</class>
 <widget class="QMainWindow" name="tela_login">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>550</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>550</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="layoutDirection">
   <enum>Qt::RightToLeft</enum>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QFrame" name="frame_global">
      <property name="minimumSize">
       <size>
        <width>500</width>
        <height>450</height>
       </size>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color:white;
padding: 0;
margin:0;</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QFrame" name="frame_login">
         <property name="maximumSize">
          <size>
           <width>440</width>
           <height>500</height>
          </size>
         </property>
         <property name="autoFillBackground">
          <bool>false</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: #FFFFFF;
border-radius: 10px;</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <widget class="QFrame" name="frame">
          <property name="geometry">
           <rect>
            <x>20</x>
            <y>190</y>
            <width>381</width>
            <height>261</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(62, 91, 185);</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <widget class="QPushButton" name="login_button">
           <property name="geometry">
            <rect>
             <x>130</x>
             <y>222</y>
             <width>121</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
    border-radius:8px;
	color:black;
	background-color:white;
}
QPushButton:hover{
    background-color: rgb(235, 235, 235);
}
QPushButton:pressed{
    background-color: rgb(199, 199, 199);
    border: 2px solid rgb(231, 231, 231);
}</string>
           </property>
           <property name="text">
            <string>Login</string>
           </property>
          </widget>
          <widget class="QLabel" name="label">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>77</y>
             <width>51</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: white;
border-radius: 10px;</string>
           </property>
           <property name="text">
            <string>CPF</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_2">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>137</y>
             <width>51</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="layoutDirection">
            <enum>Qt::LeftToRight</enum>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: white;
border-radius: 10px;</string>
           </property>
           <property name="text">
            <string>Senha</string>
           </property>
           <property name="textFormat">
            <enum>Qt::AutoText</enum>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_5">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>100</y>
             <width>71</width>
             <height>71</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>r2d2.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_4">
           <property name="geometry">
            <rect>
             <x>230</x>
             <y>10</y>
             <width>91</width>
             <height>61</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>logo_sapienss.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_7">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>30</y>
             <width>141</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:white;</string>
           </property>
           <property name="text">
            <string>Login Sapiens</string>
           </property>
          </widget>
          <widget class="QLabel" name="dados_incorretos">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>190</y>
             <width>281</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:rgb(227, 192, 34);</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLineEdit" name="cpf">
           <property name="geometry">
            <rect>
             <x>90</x>
             <y>89</y>
             <width>211</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color:white;
padding: 5px;</string>
           </property>
          </widget>
          <widget class="QLineEdit" name="senha">
           <property name="geometry">
            <rect>
             <x>90</x>
             <y>150</y>
             <width>211</width>
             <height>31</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color:white;
padding: 5px;</string>
           </property>
           <property name="echoMode">
            <enum>QLineEdit::Password</enum>
           </property>
          </widget>
          <widget class="QPushButton" name="olhar_senha">
           <property name="geometry">
            <rect>
             <x>270</x>
             <y>150</y>
             <width>31</width>
             <height>31</height>
            </rect>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background:white;</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>eye_hidden.png</normaloff>eye_hidden.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="default">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
          <zorder>login_button</zorder>
          <zorder>label_5</zorder>
          <zorder>label_4</zorder>
          <zorder>label_7</zorder>
          <zorder>dados_incorretos</zorder>
          <zorder>cpf</zorder>
          <zorder>label</zorder>
          <zorder>senha</zorder>
          <zorder>label_2</zorder>
          <zorder>olhar_senha</zorder>
         </widget>
         <widget class="QLabel" name="label_3">
          <property name="geometry">
           <rect>
            <x>160</x>
            <y>470</y>
            <width>101</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="pixmap">
           <pixmap>Logo_PGU.png</pixmap>
          </property>
          <property name="scaledContents">
           <bool>true</bool>
          </property>
          <property name="wordWrap">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QLabel" name="label_6">
          <property name="geometry">
           <rect>
            <x>160</x>
            <y>10</y>
            <width>131</width>
            <height>16</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="mouseTracking">
           <bool>false</bool>
          </property>
          <property name="text">
           <string>Bem-vindo(a)!</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_9">
          <property name="geometry">
           <rect>
            <x>80</x>
            <y>40</y>
            <width>291</width>
            <height>21</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="mouseTracking">
           <bool>false</bool>
          </property>
          <property name="text">
           <string>R²D² - Robô Distribuidor</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_11">
          <property name="geometry">
           <rect>
            <x>150</x>
            <y>70</y>
            <width>131</width>
            <height>101</height>
           </rect>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="pixmap">
           <pixmap>Cartoon-Star-Wars-PNG-R2-D2-PNG.png</pixmap>
          </property>
          <property name="scaledContents">
           <bool>true</bool>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources/images/images.qrc"/>
 </resources>
 <connections/>
</ui>
