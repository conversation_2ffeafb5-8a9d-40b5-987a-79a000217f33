from datetime import datetime


class LogMessage:
    def __init__(self, filename: str, terminal: bool):
        """
        Construtor da classe LogMessage
        @param filename: nome do arquivo log
        @param terminal: booleana que indica se deve ou não mostrar no terminal as mensagens de log
        """
        self.__filename = filename
        self.__terminal = terminal
        self.__separadores = ["#", "=", "-", "*", "%"]
        self.__quantidadeSeparadores = [50, 40, 30, 20, 20]
        self.__nivelSeparador = 0

    def log(self, msg: str) -> None:
        """
        Metodo que salva uma mensagem de log
        @param msg: mensagem de log que se deseja gravar
        """
        agora = datetime.now()
        msgCompleta = agora.strftime("%d/%m/%Y %H:%M:%S")
        msgCompleta += " : " + msg

        if self.__terminal:
            print(msgCompleta)

    def AbrirSeparador(self) -> None:
        """
        Abre uma sequência caracteres para separar um trecho em destaque
        :return:
        """
        self.log(
            self.__separadores[self.__nivelSeparador]
            * self.__quantidadeSeparadores[self.__nivelSeparador]
        )
        self.__nivelSeparador += 1

    def FecharSeparador(self) -> None:
        """
        Fecha uma sequência caracteres para separar um trecho
        :return:
        """
        self.__nivelSeparador -= 1
        self.log(
            self.__separadores[self.__nivelSeparador]
            * self.__quantidadeSeparadores[self.__nivelSeparador]
        )
