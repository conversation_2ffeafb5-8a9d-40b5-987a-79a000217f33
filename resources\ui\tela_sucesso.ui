<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>tela_sucesso</class>
 <widget class="QDialog" name="tela_sucesso">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>800</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>600</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:white;</string>
  </property>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>20</y>
     <width>251</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Relatório Geral</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="return_button">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>700</y>
     <width>321</width>
     <height>41</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>171</width>
     <height>41</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>16000000</width>
     <height>16777215</height>
    </size>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>PointingHandCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    color:white;
	background-color: rgb(62, 91, 185);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(75, 107, 209);
}
QPushButton:pressed{
    background-color: rgb(36, 81, 224);
    border: 2px solid rgb(231, 231, 231);
}</string>
   </property>
   <property name="text">
    <string>Retornar</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_12">
   <property name="geometry">
    <rect>
     <x>465</x>
     <y>745</y>
     <width>124</width>
     <height>43</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>Logo_PGU.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="wordWrap">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_13">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>690</y>
     <width>91</width>
     <height>91</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>r2d2_preto.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>450</y>
     <width>161</width>
     <height>151</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>iconeSucesso.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>410</y>
     <width>321</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Automação realizada com sucesso</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>630</y>
     <width>31</width>
     <height>31</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background:transparent;</string>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>arquivo-excel.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="gerar_relatorio">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>620</y>
     <width>341</width>
     <height>51</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>171</width>
     <height>20</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>500</width>
     <height>16777215</height>
    </size>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>PointingHandCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    color:white;
	background-color: rgb(24, 115, 24);
	border-radius:8px;
}
QPushButton:hover{
    background-color: rgb(1, 140, 1);
}
QPushButton:pressed{
    background-color: rgb(2, 153, 2);
    border: 2px solid rgb(1, 97, 1);
}</string>
   </property>
   <property name="text">
    <string>Gerar Relatório Excel</string>
   </property>
  </widget>
  <widget class="QWidget" name="grafico" native="true">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>50</y>
     <width>561</width>
     <height>351</height>
    </rect>
   </property>
  </widget>
  <zorder>label_3</zorder>
  <zorder>label_12</zorder>
  <zorder>label_13</zorder>
  <zorder>label_4</zorder>
  <zorder>return_button</zorder>
  <zorder>label_6</zorder>
  <zorder>gerar_relatorio</zorder>
  <zorder>grafico</zorder>
  <zorder>label_7</zorder>
 </widget>
 <resources>
  <include location="resources/images/images.qrc"/>
 </resources>
 <connections/>
</ui>
