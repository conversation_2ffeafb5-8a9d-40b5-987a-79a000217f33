import os
import sys


def resource_path(relative_path: str) -> str:
    """
    Get absolute path to resource - Pyinstaller and DEV.

    Args:
        relative_path (str): Path relative to the application root.

    Returns:
        str: Absolute path to the resource.
    """

    base_path = getattr(sys, "_MEIPASS", os.path.abspath("."))
    return os.path.join(base_path, relative_path)


def get_data_path(filename: str) -> str:
    """Get path to a file in the docs folder."""
    return resource_path(os.path.join("resources", "data", filename))


def get_graphics_path(filename: str) -> str:
    """Get path to a file in the docs/Arquivos_Graficos folder."""
    return resource_path(os.path.join("resources", "images", filename))


def get_ui_path(filename: str) -> str:
    """Get path to a UI file in the docs/Arquivos_Graficos folder."""
    return resource_path(os.path.join("resources", "ui", filename))


def get_env_path() -> str:
    """Get path to the .env file."""
    return resource_path(".env")
