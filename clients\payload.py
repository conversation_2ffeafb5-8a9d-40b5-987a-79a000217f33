from datetime import datetime
from random import randint


class Payload:
    def __init__(self, url, cpf, senha, csrf_token):
        self.url = url
        self.cpf = cpf
        self.senha = senha
        self.token = csrf_token
        now = datetime.now()
        self.data_hora = str(now.strftime("%Y-%m-%d %H:%M:%S"))
        self.tid = randint(2, 80)
        self.limit = 100
        self.page = 1
        mozilla = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        awk = "AppleWebKit/537.36 (KHTML, like Gecko) "
        browser = "Chrome/99.0.4844.51 Safari/537.36"
        self.user_agent = mozilla + awk + browser

    def get_header(self):
        retorno = {
            "Content-Type": "application/json",
            "Origin": self.url,
            "Referer": self.url + "/",
            "User-Agent": self.user_agent,
            "X-Requested-With": "XMLHttpRequest",
        }
        return retorno

    def get_data(self):
        retorno = {
            "_csrf_token": self.token,
            "_username": self.cpf,
            "_password": self.senha,
            "_submit": "Login",
            "Referer": self.url + "/login",
            "Origin": self.url,
        }
        return retorno

    def get_usuario(self):
        retorno = {
            "action": "SapiensMain_Usuario",
            "method": "getUsuario",
            "data": [
                {
                    "sessao": True,
                    "fetch": [
                        "colaborador",
                        "colaborador.modalidadeColaborador",
                        "colaborador.lotacoes",
                        "colaborador.lotacoes.setor",
                        "colaborador.lotacoes.setor.especieSetor",
                        "colaborador.lotacoes.setor.unidade",
                        "colaborador.lotacoes.setor.unidade.modalidadeOrgaoCentral",
                    ],
                    "filter": [
                        {"property": "colaborador.lotacoes.id", "value": "isNotNull"},
                        {
                            "property": "colaborador.lotacoes.setor.ativo",
                            "value": "eq:1",
                        },
                    ],
                    "page": 1,
                    "start": 0,
                    "limit": 25,
                }
            ],
            "type": "rpc",
            "tid": self.tid,
        }
        return retorno

    def create_tarefa(self, tarefa: dict) -> dict:
        """
        Função que organiza a informação para criação da tarefa para o Sapiens 1.0
        :param tarefa: Informações necessárias para a criação da tarefa
        :return Retorna um dicionário com os dados para chamada do post
        """
        retorno = {
            "action": "SapiensAdministrativo_Tarefa",
            "method": "createTarefa",
            "data": [
                {
                    "observacao": tarefa["teor"],
                    "postIt": "",
                    "urgente": False,
                    "dataHoraInicioPrazo": tarefa["data_h_inicio_prazo"],
                    "criadoEm": None,
                    "apagadoEm": None,
                    "atualizadoEm": None,
                    "dataHoraFinalPrazo": tarefa["data_h_final_prazo"],
                    "dataHoraConclusaoPrazo": None,
                    "pasta_id": tarefa["pasta_id"],
                    "especieTarefa_id": "",
                    "usuarioResponsavel_id": None,
                    "setorResponsavel_id": int(tarefa["setor_id"]),
                    "setorOrigem_id": int(tarefa["setor_id"]),
                    "documento_id": "",
                    "acompanhar": False,
                    "tramitar": False,
                    "arquivar": "",
                    "usuarioConclusaoPrazo_id": "",
                    "criadoPor_id": "",
                    "atualizadoPor_id": "",
                    "acompanhada": False,
                    "comunicacaoJudicial_id": tarefa["id_processo"],
                    "movimentoNacional_id": None,
                    "modalidadeRepercussao_id": tarefa["modalidade_repercucao_id"],
                    "replicar": False,
                    "migrarEtiqueta": True,
                    "redistribuida": False,
                    "distribuicaoAutomatica": True,
                    "idFormatado": "",
                }
            ],
            "type": "rpc",
            "tid": self.tid,
        }
        return retorno

    def create_tarefa_super(self, tarefa: dict) -> dict:
        """
        Função que organiza a informação para criação da tarefa para o Super Sapiens
        :param tarefa: Informações necessárias para a criação da tarefa
        :return Retorna um dicionário com os dados para chamada do post
        """
        retorno = {
            "postIt": None,
            "urgente": None,
            "observacao": tarefa["teor"],
            "localEvento": None,
            "dataHoraInicioPrazo": None,  # tarefa['data_h_inicio_prazo'],
            "dataHoraFinalPrazo": None,  # tarefa['data_h_final_prazo'],
            "processo": tarefa["processo_id"],  # 33530627 (PASTA)
            "especieTarefa": None,
            "usuarioResponsavel": None,
            "setorOrigem": None,  # int(tarefa['setor_id']),
            "setorResponsavel": int(tarefa["setor_id"]),
            "distribuicaoAutomatica": True,
            "folder": None,
            "isRelevante": None,
            "locked": None,
            "diasUteis": None,
            "prazoDias": 5,
            "blocoProcessos": None,
            "blocoResponsaveis": None,
            "usuarios": None,
            "setores": None,
            "migrar": True,
        }
        return retorno

    def get_comunicacao_judicial(
        self, unidade_id: str, periodo: dict, integrado: bool = None, page: int = None
    ) -> dict:
        if page is None:
            page = self.page
        integrado_list = []
        periodo_list = []

        data_dict = {
            "apagados": 0,
            "fetch": [
                "processoJudicial",
                "modalidadeComunicacaoJudicial",
                "modalidadeRepercussao",
                "pasta",
                "unidade",
                "setor",
                "processoJudicial.orgaoJulgador",
                "processoJudicial.orgaoJulgador.tribunal",
                "pasta.setor",
                "pasta.setor.unidade",
                "pasta.relevancias",
                "pasta.lembretes",
                "pasta.processoJudicial",
                "pasta.pessoaRepresentada",
                "pasta.assuntos",
                "pasta.assuntos.assuntoAdministrativo",
                "pasta.assuntos.assuntoAdministrativo.parent",
                "pasta.assuntos.assuntoAdministrativo.parent.parent",
                "setor.unidade",
            ],
            "filter": [
                {"property": "unidade.id", "value": "in:" + str(unidade_id)},
                {"property": "tarefa", "value": "isNull"},
            ],
            "distribuidos": 0,
            "page": page,
            "start": 0,
            "limit": self.limit,
        }

        if integrado is not None:
            integrado_list = [
                {
                    "type": "boolean",
                    "value": str(integrado).lower(),
                    "field": "integracao",
                }
            ]

        if periodo:
            periodo_list = [
                {
                    "type": "date",
                    "comparison": "gt",
                    "value": periodo["DE"],
                    "field": "criadoEm",
                },
                {
                    "type": "date",
                    "comparison": "lt",
                    "value": periodo["ATE"],
                    "field": "criadoEm",
                },
            ]

        if integrado_list or periodo_list:
            data_dict["gridfilter"] = integrado_list + periodo_list

        retorno = {
            "action": "SapiensJudicial_ComunicacaoJudicial",
            "method": "getComunicacaoJudicial",
            "data": [data_dict],
            "type": "rpc",
            "tid": self.tid,
        }
        return retorno
