<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>tela_inicial_escolhas</class>
 <widget class="QMainWindow" name="tela_inicial_escolhas">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>518</width>
    <height>550</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>550</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="layoutDirection">
   <enum>Qt::RightToLeft</enum>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="minimumSize">
       <size>
        <width>500</width>
        <height>450</height>
       </size>
      </property>
      <property name="layoutDirection">
       <enum>Qt::RightToLeft</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color:white;
padding: 0;
margin:0;</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="page1_escolhas">
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QFrame" name="frame_login">
          <property name="maximumSize">
           <size>
            <width>440</width>
            <height>500</height>
           </size>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: #FFFFFF;
border-radius: 10px;</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <widget class="QFrame" name="frame">
           <property name="geometry">
            <rect>
             <x>29</x>
             <y>190</y>
             <width>381</width>
             <height>261</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(62, 91, 185);</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <widget class="QPushButton" name="enter_button">
            <property name="geometry">
             <rect>
              <x>160</x>
              <y>195</y>
              <width>121</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:8px;
	color:black;
	background-color:white;
}
QPushButton:hover{
    background-color: rgb(235, 235, 235);
}
QPushButton:pressed{
    background-color: rgb(199, 199, 199);
    border: 2px solid rgb(231, 231, 231);
}</string>
            </property>
            <property name="text">
             <string>Login</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_r2d2_png">
            <property name="geometry">
             <rect>
              <x>80</x>
              <y>170</y>
              <width>81</width>
              <height>81</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>r2d2.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_opcao_login">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>20</y>
              <width>181</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <italic>false</italic>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: white;
font: 10pt;
font-weight: bold;</string>
            </property>
            <property name="text">
             <string>Distribuição:</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_opcao_pru">
            <property name="geometry">
             <rect>
              <x>210</x>
              <y>20</y>
              <width>161</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <italic>false</italic>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: white;
font: 10pt;
font-weight: bold;</string>
            </property>
            <property name="text">
             <string>Região:</string>
            </property>
           </widget>
           <widget class="QFrame" name="frame_escolhaPRU">
            <property name="geometry">
             <rect>
              <x>190</x>
              <y>50</y>
              <width>181</width>
              <height>81</height>
             </rect>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <widget class="QRadioButton" name="radioButton_pgu">
             <property name="geometry">
              <rect>
               <x>20</x>
               <y>10</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="checked">
            <bool>true</bool>
            </property>
             <property name="styleSheet">
              <string notr="true">color: white;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PGU</string>
             </property>
            </widget>
            <!-- <widget class="QRadioButton" name="radioButton_pru2">
             <property name="geometry">
              <rect>
               <x>20</x>
               <y>35</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PRU2</string>
             </property>
            </widget> -->
            <!-- <widget class="QRadioButton" name="radioButton_pru4">
             <property name="geometry">
              <rect>
               <x>100</x>
               <y>10</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white; background-color: gray;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PRU4</string>
             </property>
             <property name="disabled" stdset="0">
              <bool>true</bool>
             </property>
            </widget> -->
            <!-- <widget class="QRadioButton" name="radioButton_pru3">
             <property name="geometry">
              <rect>
               <x>20</x>
               <y>60</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PRU3</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="disabled" stdset="0">
              <bool>true</bool>
             </property>
            </widget> -->
            <!-- <widget class="QRadioButton" name="radioButton_pru5">
             <property name="geometry">
              <rect>
               <x>100</x>
               <y>35</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white; background-color: gray;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PRU5</string>
             </property>
             <property name="disabled" stdset="0">
              <bool>true</bool>
             </property>
            </widget>
            <widget class="QRadioButton" name="radioButton_pru6">
             <property name="geometry">
              <rect>
               <x>100</x>
               <y>60</y>
               <width>71</width>
               <height>17</height>
              </rect>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white; background-color: gray;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>PRU6</string>
             </property>
             <property name="disabled" stdset="0">
              <bool>true</bool>
             </property>
            </widget> -->
           </widget>
           <widget class="QFrame" name="frame_escolhaLogin">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>50</y>
              <width>181</width>
              <height>81</height>
             </rect>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <!-- <widget class="QRadioButton" name="radioButton_sapiens_1">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>10</y>
               <width>131</width>
               <height>17</height>
              </rect>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>Sapiens</string>
             </property>
            </widget> -->
            <widget class="QRadioButton" name="radioButton_super_sapiens">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>40</y>
               <width>141</width>
               <height>17</height>
              </rect>
             </property>
             <property name="checked">
            <bool>true</bool>
            </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: white;
font: 10pt;</string>
             </property>
             <property name="text">
              <string>Super Sapiens</string>
             </property>
             <property name="disabled" stdset="0">
              <bool>true</bool>
             </property>
            </widget>
           </widget>
           <widget class="QLabel" name="erro_escolha_distribuicao">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>131</y>
              <width>201</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:rgb(227, 192, 34);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QLabel" name="erro_escolha_regiao">
            <property name="geometry">
             <rect>
              <x>220</x>
              <y>131</y>
              <width>131</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:rgb(227, 192, 34);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </widget>
          <widget class="QLabel" name="label_3">
           <property name="geometry">
            <rect>
             <x>164</x>
             <y>460</y>
             <width>124</width>
             <height>43</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Logo_PGU.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
           <property name="wordWrap">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_6">
           <property name="geometry">
            <rect>
             <x>160</x>
             <y>10</y>
             <width>131</width>
             <height>16</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Bem-vindo(a)!</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_9">
           <property name="geometry">
            <rect>
             <x>80</x>
             <y>40</y>
             <width>291</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>R²D² - Robô Distribuidor</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_11">
           <property name="geometry">
            <rect>
             <x>150</x>
             <y>70</y>
             <width>131</width>
             <height>101</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Cartoon-Star-Wars-PNG-R2-D2-PNG.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page2_login_sapiens1">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QFrame" name="frame_login_s1">
          <property name="maximumSize">
           <size>
            <width>440</width>
            <height>500</height>
           </size>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: #FFFFFF;
border-radius: 10px;</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <widget class="QFrame" name="frame_s1">
           <property name="geometry">
            <rect>
             <x>29</x>
             <y>190</y>
             <width>381</width>
             <height>261</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(62, 91, 185);</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <widget class="QPushButton" name="login_button_s1">
            <property name="geometry">
             <rect>
              <x>130</x>
              <y>222</y>
              <width>121</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:8px;
	color:black;
	background-color:white;
}
QPushButton:hover{
    background-color: rgb(235, 235, 235);
}
QPushButton:pressed{
    background-color: rgb(199, 199, 199);
    border: 2px solid rgb(231, 231, 231);
}</string>
            </property>
            <property name="text">
             <string>Entrar</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_8">
            <property name="geometry">
             <rect>
              <x>100</x>
              <y>76</y>
              <width>51</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: white;
border-radius: 10px;</string>
            </property>
            <property name="text">
             <string>CPF</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_10">
            <property name="geometry">
             <rect>
              <x>100</x>
              <y>137</y>
              <width>51</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: white;
border-radius: 10px;</string>
            </property>
            <property name="text">
             <string>Senha</string>
            </property>
            <property name="textFormat">
             <enum>Qt::AutoText</enum>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QLabel" name="label_12">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>100</y>
              <width>71</width>
              <height>71</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>r2d2.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_13">
            <property name="geometry">
             <rect>
              <x>190</x>
              <y>15</y>
              <width>51</width>
              <height>51</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>logo_sapienss.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_14">
            <property name="geometry">
             <rect>
              <x>100</x>
              <y>30</y>
              <width>90</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:white;</string>
            </property>
            <property name="text">
             <string>SAPIENS</string>
            </property>
           </widget>
           <widget class="QLabel" name="dados_incorretos_s1">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>190</y>
              <width>320</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:rgb(227, 192, 34);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QLineEdit" name="cpf_s1">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>89</y>
              <width>211</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color:white;
padding: 5px;</string>
            </property>
            <property name="inputMask">
             <string/>
            </property>
            <property name="cursorPosition">
             <number>0</number>
            </property>
           </widget>
           <widget class="QLineEdit" name="senha_s1">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>150</y>
              <width>211</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color:white;
padding: 5px;</string>
            </property>
            <property name="echoMode">
             <enum>QLineEdit::Password</enum>
            </property>
           </widget>
           <widget class="QPushButton" name="olhar_senha_s1">
            <property name="geometry">
             <rect>
              <x>270</x>
              <y>150</y>
              <width>31</width>
              <height>31</height>
             </rect>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background:white;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>eye_hidden.png</normaloff>eye_hidden.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="default">
             <bool>true</bool>
            </property>
            <property name="flat">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="voltar_s1">
            <property name="geometry">
             <rect>
              <x>330</x>
              <y>220</y>
              <width>51</width>
              <height>41</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>Voltar.png</normaloff>Voltar.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
           </widget>
           <zorder>login_button_s1</zorder>
           <zorder>label_12</zorder>
           <zorder>label_13</zorder>
           <zorder>label_14</zorder>
           <zorder>dados_incorretos_s1</zorder>
           <zorder>cpf_s1</zorder>
           <zorder>senha_s1</zorder>
           <zorder>olhar_senha_s1</zorder>
           <zorder>label_8</zorder>
           <zorder>label_10</zorder>
           <zorder>voltar_s1</zorder>
          </widget>
          <widget class="QLabel" name="label_15">
           <property name="geometry">
            <rect>
             <x>164</x>
             <y>460</y>
             <width>124</width>
             <height>41</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Logo_PRU2R.jpeg</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
           <property name="wordWrap">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_16">
           <property name="geometry">
            <rect>
             <x>160</x>
             <y>10</y>
             <width>131</width>
             <height>16</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Bem-vindo(a)!</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_17">
           <property name="geometry">
            <rect>
             <x>80</x>
             <y>40</y>
             <width>291</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>R²D² - Robô Distribuidor</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_18">
           <property name="geometry">
            <rect>
             <x>150</x>
             <y>70</y>
             <width>131</width>
             <height>101</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Cartoon-Star-Wars-PNG-R2-D2-PNG.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page3_login_super_sapiens">
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QFrame" name="frame_login_ss">
          <property name="maximumSize">
           <size>
            <width>440</width>
            <height>500</height>
           </size>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: #FFFFFF;
border-radius: 10px;</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <widget class="QFrame" name="frame_ss">
           <property name="geometry">
            <rect>
             <x>29</x>
             <y>190</y>
             <width>381</width>
             <height>261</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(62, 91, 185);</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <widget class="QPushButton" name="login_button_ss">
            <property name="geometry">
             <rect>
              <x>130</x>
              <y>222</y>
              <width>121</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:8px;
	color:black;
	background-color:white;
}
QPushButton:hover{
    background-color: rgb(235, 235, 235);
}
QPushButton:pressed{
    background-color: rgb(199, 199, 199);
    border: 2px solid rgb(231, 231, 231);
}</string>
            </property>
            <property name="text">
             <string>Entrar</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_19">
            <property name="geometry">
             <rect>
              <x>100</x>
              <y>77</y>
              <width>51</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: white;
border-radius: 10px;</string>
            </property>
            <property name="text">
             <string>E-mail</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_20">
            <property name="geometry">
             <rect>
              <x>100</x>
              <y>137</y>
              <width>51</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: white;
border-radius: 10px;</string>
            </property>
            <property name="text">
             <string>Senha</string>
            </property>
            <property name="textFormat">
             <enum>Qt::AutoText</enum>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QLabel" name="label_21">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>100</y>
              <width>71</width>
              <height>71</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>r2d2.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_23">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>30</y>
              <width>160</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:white;</string>
            </property>
            <property name="text">
             <string>SUPER SAPIENS</string>
            </property>
           </widget>
           <widget class="QLabel" name="dados_incorretos_ss">
            <property name="geometry">
             <rect>
              <x>30</x>
              <y>190</y>
              <width>320</width>
              <height>21</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:rgb(227, 192, 34);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QLineEdit" name="email">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>89</y>
              <width>256</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color:white;
padding: 5px;</string>
            </property>
           </widget>
           <widget class="QLineEdit" name="senha_ss">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>150</y>
              <width>256</width>
              <height>31</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color:white;
padding: 5px;</string>
            </property>
            <property name="inputMask">
             <string/>
            </property>
            <property name="echoMode">
             <enum>QLineEdit::Password</enum>
            </property>
           </widget>
           <widget class="QPushButton" name="olhar_senha_ss">
            <property name="geometry">
             <rect>
              <x>315</x>
              <y>150</y>
              <width>31</width>
              <height>31</height>
             </rect>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background:white;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>eye_hidden.png</normaloff>eye_hidden.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="default">
             <bool>true</bool>
            </property>
            <property name="flat">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_22">
            <property name="geometry">
             <rect>
              <x>230</x>
              <y>23</y>
              <width>61</width>
              <height>41</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>SuperSapiens.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="voltar_ss">
            <property name="geometry">
             <rect>
              <x>330</x>
              <y>220</y>
              <width>51</width>
              <height>41</height>
             </rect>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>Voltar.png</normaloff>Voltar.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
           </widget>
           <zorder>login_button_ss</zorder>
           <zorder>label_21</zorder>
           <zorder>label_23</zorder>
           <zorder>dados_incorretos_ss</zorder>
           <zorder>email</zorder>
           <zorder>senha_ss</zorder>
           <zorder>olhar_senha_ss</zorder>
           <zorder>label_20</zorder>
           <zorder>label_19</zorder>
           <zorder>label_22</zorder>
           <zorder>voltar_ss</zorder>
          </widget>
          <widget class="QLabel" name="label_24">
           <property name="geometry">
            <rect>
             <x>164</x>
             <y>460</y>
             <width>124</width>
             <height>41</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>logoPGU.jpg</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
           <property name="wordWrap">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_25">
           <property name="geometry">
            <rect>
             <x>160</x>
             <y>10</y>
             <width>131</width>
             <height>16</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Bem-vindo(a)!</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_26">
           <property name="geometry">
            <rect>
             <x>80</x>
             <y>40</y>
             <width>291</width>
             <height>21</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>R²D² - Robô Distribuidor</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_27">
           <property name="geometry">
            <rect>
             <x>150</x>
             <y>70</y>
             <width>131</width>
             <height>101</height>
            </rect>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap>Cartoon-Star-Wars-PNG-R2-D2-PNG.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources/images/images.qrc"/>
  <include location="resources/images/images.qrc"/>
 </resources>
 <connections/>
</ui>
