import datetime
import json
import os
import sys
import time

import pandas as pd
import requests
from bs4 import BeautifulSoup as bs

from clients.super_sapiens import Super_Sapiens
from services.logger import LogMessage
from services.pgu import (
    DISTRIBUIDO,
    ERRO_STATUSINTEGRACAO,
    NAO_DISTRIBUIDO,
    SUGESTAO,
)
from utils.resource_path import get_data_path


class Distribuidor:
    def __init__(self, logger: LogMessage):
        self.super_sapiens = Super_Sapiens(logger=logger)
        self.sapiens_info = {}
        self.resp_intimacoes = []
        self.total = 0
        self.i_intimacao = {}
        self.i_etiqueta = {}
        self.lembrete_relatorio = ""
        self.status_processo = {}
        self.contador_relatorio = {}
        self.contador_relatorio_list = []
        self.num_jud_list = []
        self.nup_list = []
        self.assunto_list = []
        self.teor_list = []
        self.lembrete_list = []
        self.ult_setor_list = []
        self.modalidade_list = []
        self.etiqueta_final_list = []
        self.status_processo_list = []
        self.classe_processual_list = []
        self.orgao_julgador_list = []
        self.id_processo_list = []
        self.fonte_dados_list = []
        self.__relatorio_gerado = None
        self.dict_msg = self.__get_json("dict_msg.json")
        self.__logger = logger

    def __get_json(self, file: str) -> json:
        file_path = get_data_path(file)
        with open(file_path, "r", encoding="utf-8") as file_json:
            retorno = json.load(file_json)
        return retorno

    def __form_etiqueta(self, etiqueta):
        """
        Função para formatar a etiqueta do processo, fazendo validações para que
        a etiqueta não sobreponha outras que não foram feitas pelo distribuidor.
        :param etiqueta: Texto que deve ser informado toda vez que a função é chamada.
        """

        self.__logger.log(f"Formatando a etiqueta {etiqueta} ")
        if self.i_intimacao["POSTIT"] == "":
            self.i_intimacao["POSTIT"] = etiqueta
        else:
            ##### ALTERADO POR FERNANDO PRU6 para RETIRAR "(VIA INTEGRAÇÃO S2 - MNI222)" DA ETIQUETA
            etiqueta_sem_formatacao = self.i_intimacao["POSTIT"]
            if "(VIA INTEGRAÇÃO S2 - MNI222)" in etiqueta_sem_formatacao:
                etiqueta_sem_formatacao = etiqueta_sem_formatacao.replace(
                    "(VIA INTEGRAÇÃO S2 - MNI222)", ""
                )
                self.i_intimacao["POSTIT"] = etiqueta_sem_formatacao

            if etiqueta in self.i_intimacao["POSTIT"]:
                etiqueta_sem_formatacao = self.i_intimacao["POSTIT"]
                etiqueta_sem_formatacao = etiqueta_sem_formatacao.replace(
                    etiqueta, etiqueta
                )

                self.i_intimacao["POSTIT"] = etiqueta_sem_formatacao
            else:
                self.i_intimacao["POSTIT"] = f"{self.i_intimacao['POSTIT']}.{etiqueta}"

    def __formatar_lembrete(self, lembretes: list) -> list:
        lembretes_f = []
        for lembrete in lembretes:
            try:
                lembretes_f.append(bs(lembrete["conteudo"], "html.parser").text)
            except Exception as e:
                self.__logger.log(f"Erro ao formatar lembrete. Erro gerado foi {e}")

        self.lembrete_relatorio = " | ".join(tuple(lembretes_f))

        return lembretes_f

    def __verificar_erros(
        self, requisicao: object, dicionario_de_mensagens: dict, etiquetar: bool
    ) -> None:
        status_req = requisicao.status_code
        str_status_req = str(status_req)
        if str_status_req in dicionario_de_mensagens:
            erro = f"ERRO {str_status_req} - {dicionario_de_mensagens[str_status_req]}"
            erro_formatado = erro.upper()
            self.__logger.log(erro_formatado)
            self.__form_etiqueta(f"R2D2: {erro_formatado}")
            self.gravar_etiqueta(etiquetar)
            self.status_processo["FLAG"] = f"{NAO_DISTRIBUIDO} - {erro_formatado}"
            self.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO
        else:
            erro_formatado = "ERRO NÃO IDENTIFICADO"
            self.__logger.log(erro_formatado)
            self.__form_etiqueta(f"R2D2: {erro_formatado}")
            self.gravar_etiqueta(etiquetar)
            self.status_processo["FLAG"] = f"{NAO_DISTRIBUIDO} - {erro_formatado}"
            self.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO

    def __verificar_status_code(self, requisicao: requests):
        status_req = requisicao.status_code
        str_status_req = str(status_req)
        self.__logger.log(f"Status da requisição foi {str_status_req}")
        if str_status_req in self.dict_msg:
            self.__logger.log(f"{self.dict_msg[str_status_req]}")
            if str_status_req[0] == "2":
                return "OK"
            elif str_status_req in self.dict_msg:
                self.__logger.log("*" * 30)
                self.__logger.log(f"Erro : {str_status_req}")
                self.__logger.log(
                    f"Descrição do erro : {self.dict_msg[str_status_req]}"
                )
                self.__logger.log(f"Conteúdo do retorno: {requisicao.content}")
                self.__logger.log("*" * 30)
                return "ERRO"
            else:
                return f"ERRO {str_status_req} "
        else:
            return "ERRO"

    def login(self, usuario: str, senha: str) -> dict:
        retorno_sapiens = {}
        if self.sapiens_info["VERSAO"] == "SUPER":
            retorno_sapiens = self.super_sapiens.login(usuario, senha)

        return retorno_sapiens

    def zerando_variaves(self) -> None:
        try:
            self.i_intimacao = {}
            self.i_etiqueta = {}

            self.status_processo = {}
            self.contador_relatorio = {}

            self.lembrete_relatorio = ""
        except Exception as e:
            self.__logger.log(
                f"Erro no exception distribuidor zerando variáveis: {str(e)}"
            )

    def criar_relatorio(self, relatorio: object) -> object:
        """
        Cria um relátorio em excel de acordo com os dados do dicionàrio
        :param relátorio:
        Parametro contêm apenas o nome do relátorio formatado com data e hora
        :return:
        retorna o relátorio gerado com nome e data corretos e dados retirados do dicionário
        """
        try:
            dicionario = {
                "Numero_jud": self.num_jud_list,
                "NUP": self.nup_list,
                "Classe Processual": self.classe_processual_list,
                "Orgão_Julgador": self.orgao_julgador_list,
                "Assunto": self.assunto_list,
                "Observação": self.teor_list,
                "Lembrete": self.lembrete_list,
                "Ultimo_Setor": self.ult_setor_list,
                "Modalidade": self.modalidade_list,
                ####INSERIDO POR FERNANDO PRU6 (Campo ORIGEMDO RELATÓRIO é o [FONTE_DADOS] ##############
                "ORIGEM": self.fonte_dados_list,
                ###############################################
                "Etiqueta_final": self.etiqueta_final_list,
                # "Status_do_Processo": self.status_processo_list,
            }
            relatorio_df = pd.DataFrame(dicionario)

            ################### INSERIDO POR FERNANDO PRU6 #############
            # Ajustar o índice para começar em 1
            relatorio_df.index = relatorio_df.index + 1
            # Salvar o DataFrame em um arquivo Excel
            relatorio_df.to_excel(
                relatorio, index=True
            )  # index=True para incluir o índice no Excel
            # Atribuir o nome do relatório gerado ao atributo da classe
            self.__relatorio_gerado = relatorio

            ########### alterado por fernando PRU6
            # self.__relatorio_gerado = relatorio_df.to_excel(relatorio)

            ############################################################
            # self.__relatorio_gerado = relatorio_df.to_excel(relatorio)
            """ self.relatorio_gerado = relatorio_df.to_excel(
                relatorio,
                sheet_name="RESULTADO",
                index=False
            ) """
        except Exception as e:
            print(f"Erro no criar_relatorio: {str(e)}")
        return self.__relatorio_gerado

    def calcular_estatisticas_de_uso(self):
        """
        Metodo para gravar/atualizar dados a respeito da execução a respeito de como foi a distribuição de processos
        """

        data = datetime.now()
        # criando nome do arquivo de estatisticas

        if getattr(sys, "frozen", False):
            folder = os.path.dirname(sys.executable)
        else:
            folder = os.path.dirname(os.path.abspath(sys.modules["__main__"].__file__))
        if not os.path.exists(os.path.join(folder, "statistics")):
            os.makedirs(os.path.join(folder, "statistics"))

        nome_arquivo_estatisticas = f"R2D2_{data.year}_{data.month:0>2}.xlsx"
        nome_arquivo_estatisticas = os.path.join(
            folder, "statistics", nome_arquivo_estatisticas
        )

        # recuperando as estatisticas de uso
        temp_dict = self.calcular_sugestoes_e_distribuicoes()
        dict_estatisticas = {}
        # adicionando a coluna do dia e hora da da medição
        dict_estatisticas["instante"] = (
            f"{data.day:0>2}/{data.month:0>2}/{data.year} {data.hour:0>2}:{data.minute:0>2}"
        )
        dict_estatisticas.update(temp_dict)

        #################### INSERIDO POR FERNANDO PRU6 - COLUNA DE TOTAL DE INTIMAÇÕES ################

        dict_estatisticas["total_intimacoes"] = 0  # Inicializa com 0

        dict_estatisticas.update(temp_dict)

        # Calculando o total_intimacoes como a soma dos valores numéricos das chaves anteriores, exceto 'instante'
        total_intimacoes = 0
        for key, value in dict_estatisticas.items():
            if key != "instante" and isinstance(
                value, (int, float)
            ):  # Ignora 'instante' e soma apenas valores numéricos
                total_intimacoes += value
        dict_estatisticas["total_intimacoes"] = (
            total_intimacoes  # Atualiza a chave total_intimacoes
        )

        # colunas = dict_estatisticas.keys()
        # lista = []
        """
        try:
            with open(nome_arquivo_estatisticas, 'r', encoding='utf-8') as csv_file:
                read_csv = csv.DictReader(csv_file,delimiter=';')
                for linha in read_csv:
                    lista.append(linha)
        except FileNotFoundError as e:
            self.__logger.log(f'Erro na abertura do arquivo de estatísticas {nome_arquivo_estatisticas}')
            self.__logger.log(f'Erro levantado foi {e}')
        # adicionando a estatistica atual
        lista.append(dict_estatisticas)
        # abrindo arquivo como csv
        with open(nome_arquivo_estatisticas, 'w+', encoding='utf-8', newline='') as csv_file:
            write_csv = csv.DictWriter(csv_file, delimiter=';', fieldnames=colunas )
            write_csv.writeheader()
            write_csv.writerows(lista)
        """

        try:
            if os.path.exists(nome_arquivo_estatisticas):
                # arquivo ja existe. incluíndo nova linha
                estatistica_df = pd.read_excel(nome_arquivo_estatisticas)
                estatistica_df.loc[len(estatistica_df)] = dict_estatisticas
            else:
                estatistica_df = pd.DataFrame([dict_estatisticas])
            estatistica_df.to_excel(nome_arquivo_estatisticas, index=False)
        except Exception as e:
            self.__logger.log("Erro na geração do arquivo de estatísticas")
            self.__logger.log(f"Erro levantado foi {e}")

        self.__logger.log(
            "%%%%%%%%%%%%%%%%%%% ESTATISTICAS DESSA EXECUÇÃO %%%%%%%%%%%%%%%%%%%"
        )
        for key, value in dict_estatisticas.items():
            self.__logger.log(f"\t{key.upper()}: {value} ")
        self.__logger.log(
            "%%%%%%%%%%%%%%%%%%% FIM DAS ESTATISTICAS DESSA EXECUÇÃO %%%%%%%%%%%%"
        )

    def calcular_sugestoes_e_distribuicoes(self) -> dict:
        """
        Calcula a quantidade de processos que foram distribuidos, sugeridos e não-distribuidos.
        Armazena os dados em variáveis que serão usadas para análises posteriores e a criação do gráfico
        :return:
        retorna os dados salvos nas variáveis
        """
        print(
            "Armazena os dados em variáveis que serão usadas para análises posteriores e a criação do gráfico\n"
        )

        distribuidos = self.contador_relatorio_list.count(DISTRIBUIDO)
        sugestoes = self.contador_relatorio_list.count(SUGESTAO)
        nao_distribuidos = self.contador_relatorio_list.count(NAO_DISTRIBUIDO)
        nao_corretamente_integrados = self.contador_relatorio_list.count(
            ERRO_STATUSINTEGRACAO
        )
        return {
            "distribuidos": distribuidos,
            "nao_distribuidos": nao_distribuidos,
            "falha_integracao": nao_corretamente_integrados,
            "sugestoes": sugestoes,
        }

    def extrair_dados_intimacao(self, intimacao: dict) -> None:
        """
        Busca os dados da intimação e salva cada um em uma chave do dicionário. Considera que tipo de sistema
        está se utilizando. O Sapiens ou o SuperSapiens
        :param intimacao : Item da lista de intimações enviadas
        """
        if self.sapiens_info["VERSAO"] == "SUPER":
            self.__logger.log("Extraindo dados com base em um retorno do Super Sapiens")
            return self.__extrair_dados_intimacao_supersapiens(intimacao)

    def __extrair_dados_intimacao_supersapiens(self, intimacao: dict) -> None:
        """
        Extraindo dados da intimação quando dados foram extraídos a partir do Super Sapiens (SS)
        Campos de dicionarios e estrutururas de chaves são diferentes do Sapiens 1.0
        :param intimacao: Item da lista de intimições recuperadas do Super Sapiens
        """

        self.i_intimacao["ID"] = ""
        if "id" in intimacao:
            self.i_intimacao["ID"] = intimacao["id"]

        self.i_intimacao["NUMERO"] = ""
        if "numero" in intimacao:
            self.i_intimacao["NUMERO"] = intimacao["numero"]

        self.i_intimacao["POSTIT"] = ""
        # if "postIt" in intimacao:  # observação
        #     self.i_intimacao["POSTIT"] = intimacao["postIt"]

        self.i_intimacao["MODALIDADE_REPERCUSSAO_ID"] = ""
        if "modalidadeRepercussao" in intimacao:
            self.i_intimacao["MODALIDADE_REPERCUSSAO_ID"] = intimacao[
                "modalidadeRepercussao"
            ]["id"]

        self.i_intimacao["ID_COMUNICACAO"] = ""
        if "idComunicacao" in intimacao:
            self.i_intimacao["ID_COMUNICACAO"] = intimacao["idComunicacao"]

        self.i_intimacao["MOD_COM_JUD_ID"] = ""
        if "modalidadeComunicacaoJudicial" in intimacao:
            self.i_intimacao["MOD_COM_JUD_ID"] = intimacao[
                "modalidadeComunicacaoJudicial"
            ]["id"]

        self.i_intimacao["UNIDADE_ID"] = ""
        if "unidade" in intimacao:
            self.i_intimacao["UNIDADE_ID"] = intimacao["unidade"]["id"]

        self.i_intimacao["STATUS"] = ""
        if "status" in intimacao:
            self.i_intimacao["STATUS"] = intimacao["status"]

        self.i_intimacao["STATUS_INTEG"] = ""
        if "statusIntegracao" in intimacao:
            self.i_intimacao["STATUS_INTEG"] = intimacao["statusIntegracao"]

        self.i_intimacao["CENT_DISTRIB"] = ""
        if "centenaDistribuicao" in intimacao:
            self.i_intimacao["CENT_DISTRIB"] = intimacao["centenaDistribuicao"]

        self.i_intimacao["DIGITO_DISTRIB"] = ""
        if "digitoDistribuicao" in intimacao:
            self.i_intimacao["DIGITO_DISTRIB"] = intimacao["digitoDistribuicao"]

        self.i_intimacao["POLO_ENTIDADE"] = ""
        if "poloEntidade" in intimacao:
            self.i_intimacao["POLO_ENTIDADE"] = intimacao["poloEntidade"]

        self.i_intimacao["INTEGRACAO"] = ""
        if "integracao" in intimacao:
            self.i_intimacao["INTEGRACAO"] = intimacao["integracao"]

        self.i_intimacao["ID_CONSULTANTE"] = ""
        if "idConsultante" in intimacao:
            self.i_intimacao["ID_CONSULTANTE"] = intimacao["idConsultante"]

        self.i_intimacao["DATA_H_INICIO_PRAZO"] = ""
        if "dataHoraInicioPrazo" in intimacao:
            self.i_intimacao["DATA_H_INICIO_PRAZO"] = intimacao["dataHoraInicioPrazo"]

        self.i_intimacao["DATA_H_FINAL_PRAZO"] = ""
        if "dataHoraFinalPrazo" in intimacao:
            self.i_intimacao["DATA_H_FINAL_PRAZO"] = intimacao["dataHoraFinalPrazo"]

        self.i_intimacao["CRIADO_EM"] = ""
        if "criadoEm" in intimacao:
            self.i_intimacao["CRIADO_EM"] = intimacao["criadoEm"]

        self.i_intimacao["ATUALIZADO_EM"] = ""
        if "atualizadoEm" in intimacao:
            self.i_intimacao["ATUALIZADO_EM"] = intimacao["atualizadoEm"]

        self.i_intimacao["ULTIMO_SETOR"] = ""
        if "setor" in intimacao:
            if "nome" in intimacao["setor"]:
                self.i_intimacao["ULTIMO_SETOR"] = intimacao["setor"]["nome"]

        self.i_intimacao["MODALIDADE"] = ""
        if "modalidadeComunicacaoJudicial" in intimacao:
            if "valor" in intimacao["modalidadeComunicacaoJudicial"]:
                self.i_intimacao["MODALIDADE"] = intimacao[
                    "modalidadeComunicacaoJudicial"
                ]["valor"]

        self.i_intimacao["TEOR"] = ""
        self.i_etiqueta["TEOR"] = ""
        if "teor" in intimacao:
            # removido por causa problemas na integração com o Sapiens
            # self.i_intimacao['TEOR'] = intimacao['teor']
            self.i_etiqueta["TEOR"] = self.i_intimacao["TEOR"]

        self.i_intimacao["SETOR_ID"] = ""
        self.i_etiqueta["SETOR_ID"] = ""
        # AJUSTANDO SETOR A PARTIR DO SISTEMA UTILIZADO (SAPIENS OU SS)
        if "setor" in intimacao:
            if "id" in intimacao["setor"]:
                self.i_intimacao["SETOR_ID"] = intimacao["setor"]["id"]
                self.i_etiqueta["SETOR_ID"] = self.i_intimacao["SETOR_ID"]

        self.i_intimacao["NUMERO_ALTERNATIVO"] = ""
        self.i_intimacao["PASTA_ID"] = ""
        self.i_intimacao["NUP"] = ""
        self.i_intimacao["NIVEL_SIGILO"] = ""
        self.i_intimacao["PROC_JUD_ID"] = ""
        self.i_intimacao["ASSUNTOS"] = []
        self.i_intimacao["LEMBRETES"] = []
        self.i_intimacao["VALOR_ECONOMICO"] = ""

        if "vinculacaoProcessoJudicialProcesso" in intimacao:
            if "processo" in intimacao["vinculacaoProcessoJudicialProcesso"]:
                # valor economico
                if (
                    "valorEconomico"
                    in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]
                ):
                    try:
                        self.i_intimacao["VALOR_ECONOMICO"] = float(
                            intimacao["vinculacaoProcessoJudicialProcesso"]["processo"][
                                "valorEconomico"
                            ]
                        )
                    except Exception as e:
                        self.__logger.log(
                            "Erro na conversão do valor econômico no Super Sapiens"
                        )
                        self.__logger.log(f"Erro levantado foi: {e}")
                        self.i_intimacao["VALOR_ECONOMICO"] = intimacao[
                            "vinculacaoProcessoJudicialProcesso"
                        ]["processo"]["valorEconomico"]
                # id da pasta (processo)
                if "id" in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]:
                    self.i_intimacao["PASTA_ID"] = intimacao[
                        "vinculacaoProcessoJudicialProcesso"
                    ]["processo"]["id"]
                # NUP do processo
                if "NUP" in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]:
                    self.i_intimacao["NUP"] = intimacao[
                        "vinculacaoProcessoJudicialProcesso"
                    ]["processo"]["NUP"]
                # obtendo lista de assuntos do processo
                if (
                    "assuntos"
                    in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]
                ):
                    for assunto in intimacao["vinculacaoProcessoJudicialProcesso"][
                        "processo"
                    ]["assuntos"]:
                        assunto_dict = {}
                        if "assuntoAdministrativo" in assunto:
                            if "nome" in assunto["assuntoAdministrativo"]:
                                assunto_dict["NOME"] = assunto["assuntoAdministrativo"][
                                    "nome"
                                ]
                                assunto_dict["ID_ADM"] = assunto[
                                    "assuntoAdministrativo"
                                ]["id"]
                                assunto_dict["ATIVO"] = assunto[
                                    "assuntoAdministrativo"
                                ]["ativo"]
                                assunto_dict["ID"] = assunto["id"]
                                assunto_dict["PRINCIPAL"] = assunto["principal"]
                                self.i_intimacao["ASSUNTOS"].append(assunto_dict)
                # obtendo lembretes do processo
                if (
                    "lembretes"
                    in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]
                ):
                    self.i_intimacao["LEMBRETES"] = self.i_intimacao[
                        "LEMBRETES"
                    ] + self.__formatar_lembrete(
                        intimacao["vinculacaoProcessoJudicialProcesso"]["processo"][
                            "lembretes"
                        ]
                    )

        if "processoJudicial" in intimacao:
            if "numeroAlternativo" in intimacao["processoJudicial"]:
                self.i_intimacao["NUMERO_ALTERNATIVO"] = intimacao["processoJudicial"][
                    "numeroAlternativo"
                ]

            if "nivelSigilo" in intimacao["processoJudicial"]:
                self.i_intimacao["NIVEL_SIGILO"] = intimacao["processoJudicial"][
                    "nivelSigilo"
                ]

            self.i_intimacao["PROC_JUD_ID"] = intimacao["processoJudicial"]["id"]

        try:
            self.i_intimacao["FONTE_DADOS"] = ""
            if "fonteDados" in intimacao:
                if "sigla" in intimacao["fonteDados"]:
                    self.i_intimacao["FONTE_DADOS"] = intimacao["fonteDados"]["sigla"]
            elif "vinculacaoProcessoJudicialProcesso" in intimacao:
                if "processo" in intimacao["vinculacaoProcessoJudicialProcesso"]:
                    if (
                        "any"
                        in intimacao["vinculacaoProcessoJudicialProcesso"]["processo"]
                    ):
                        if (
                            "fonteDados"
                            in intimacao["vinculacaoProcessoJudicialProcesso"][
                                "processo"
                            ]["any"]
                        ):
                            if (
                                "sigla"
                                in intimacao["vinculacaoProcessoJudicialProcesso"][
                                    "processo"
                                ]["any"]["fonteDados"]
                            ):
                                self.i_intimacao["FONTE_DADOS"] = intimacao[
                                    "vinculacaoProcessoJudicialProcesso"
                                ]["processo"]["any"]["fonteDados"]["sigla"]

            self.i_intimacao["CLASSE_PROCESSUAL"] = ""
            self.i_intimacao["ORGAO_JULGADOR"] = ""

            if "processoJudicial" in intimacao:
                if self.i_intimacao["NUP"] and self.i_intimacao["PASTA_ID"]:
                    if "classeNacional" in intimacao["processoJudicial"]:
                        self.i_intimacao["CLASSE_PROCESSUAL"] = intimacao[
                            "processoJudicial"
                        ]["classeNacional"]["nome"]

                if "orgaoJulgador" in intimacao["processoJudicial"]:
                    if "nome" in intimacao["processoJudicial"]["orgaoJulgador"]:
                        self.i_intimacao["ORGAO_JULGADOR"] = intimacao[
                            "processoJudicial"
                        ]["orgaoJulgador"]["nome"]

        except Exception as e:
            self.__logger.log(f"Erro no exception final extrair supersapiens: {str(e)}")

    def gravar_etiqueta(self, ativo: bool = True) -> None:
        """
        Apenas para realizar a gravação da etiqueta que já vem formatada e pronta para uso da função form_etiqueta.
        Desse modo essa função apenas manda o payload para o sapiens com os novos dados que devem ser inseridos
        na etiqueta de cada processo.
        :return:
        Por ser uma função que apenas envia dados para o sapiens, não há retorno.
        """

        # print("ENTROO Distribuidor gravar_etiqueta -=-=-=-=-=-=-=-=-=-=-=")

        ########### inserido por Fernando PRU6 para testar alterar INTIMAÇÃO PARA UM NUP ESPECIFICO EX: 00475018542202438
        """
        if self.i_intimacao['NUP'] == "00475018666202413":
            nupaux = self.i_intimacao['NUP']
            self.__logger.log(f'ENTROO  Distribuidor gravar_etiquea e é NUP {nupaux}')
            print(f"ENTROO  Distribuidor gravar_etiquea e é NUP {nupaux}")

            ####### PARA TESTAR SE ESTÁ GRAVANDO INTIMAÇÃO ESPECÍFICA PARA UM NUP ESPECIFICO EX.00475018666202413, REMOVER O COMENTÁRIO DO TRECHO ABAIXO #################
            ##################################################################################
            
        """

        try:
            if ativo:
                update = {
                    "id_processo": self.i_intimacao["ID"],
                    "numero": self.i_intimacao["NUMERO"],
                    "numero_alternativo": self.i_intimacao["NUMERO_ALTERNATIVO"],
                    "fonte_dados": self.i_intimacao["FONTE_DADOS"],
                    "postit": self.i_intimacao["POSTIT"],
                    "modalidade_repercucao_id": self.i_intimacao[
                        "MODALIDADE_REPERCUSSAO_ID"
                    ],
                    "pasta_id": self.i_intimacao["PASTA_ID"],
                    "id_comunicacao": self.i_intimacao["ID_COMUNICACAO"],
                    "teor_gravar_etiqueta": self.i_etiqueta["TEOR"],
                    "nivel_sigilo": self.i_intimacao["NIVEL_SIGILO"],
                    "data_h_inicio_prazo": self.i_intimacao["DATA_H_INICIO_PRAZO"],
                    "mod_com_jud_id": self.i_intimacao["MOD_COM_JUD_ID"],
                    "setor_id_gravar_etiqueta": self.i_etiqueta["SETOR_ID"],
                    "unidade_id": self.i_intimacao["UNIDADE_ID"],
                    "proc_jud_id": self.i_intimacao["PROC_JUD_ID"],
                    "status": self.i_intimacao["STATUS"],
                    "status_integ": self.i_intimacao["STATUS_INTEG"],
                    "criado_em": self.i_intimacao["CRIADO_EM"],
                    "atualizado_em": self.i_intimacao["ATUALIZADO_EM"],
                    "cent_distrib": self.i_intimacao["CENT_DISTRIB"],
                    "digito_distrib": self.i_intimacao["DIGITO_DISTRIB"],
                    "polo_entidade": self.i_intimacao["POLO_ENTIDADE"],
                    "integracao": self.i_intimacao["INTEGRACAO"],
                    "id_consultante": self.i_intimacao["ID_CONSULTANTE"],
                    "idFormatado": (
                        f"id: {self.i_intimacao['ID']} - "
                        f"{self.i_intimacao['NUMERO']} - "
                        f"{self.i_intimacao['MODALIDADE']} - "
                        f"prazo final em {self.i_intimacao['DATA_H_FINAL_PRAZO']}"
                    ),
                }

                if self.sapiens_info["VERSAO"] == "SAPIENS":
                    #################################################
                    # self.__logger.log(f'ENTROO  Distribuidor gravar_etiquea self.sapiens_info[VERSAO] == "SAPIENS"')

                    self.sapiens.update_comunicacao_judicial(update)
                if self.sapiens_info["VERSAO"] == "SUPER":
                    ########################################################
                    # self.__logger.log(f'ENTROO  Distribuidor gravar_etiquea self.sapiens_info[VERSAO] == "SUPER"')
                    pass

        except Exception as e:
            self.__logger.log(f"Erro no exception final gravar etiqueta : {str(e)}")

        # self.__logger.log("SAIU distribuidor Class Distribuidor gravar etiqueta")

    def distribuir_processo(self, ativo: bool = True, etiquetar: bool = True) -> None:
        if self.i_intimacao["NUP"] and self.i_intimacao["SETOR_ID"]:
            if ativo:
                contador = 1
                while True:
                    self.__logger.AbrirSeparador()
                    requisicao = None
                    if self.sapiens_info["VERSAO"] == "SUPER":
                        # pass
                        tarefa = {
                            "teor": self.i_intimacao["TEOR"],
                            "data_h_inicio_prazo": self.i_intimacao[
                                "DATA_H_INICIO_PRAZO"
                            ],
                            "data_h_final_prazo": self.i_intimacao[
                                "DATA_H_FINAL_PRAZO"
                            ],
                            "pasta_id": self.i_intimacao["PASTA_ID"],
                            "setor_id": self.i_intimacao["SETOR_ID"],
                            "processo_id": self.i_intimacao[
                                "PASTA_ID"
                            ],  # self.i_intimacao['ID'],
                            "modalidade_repercucao_id": self.i_intimacao[
                                "MODALIDADE_REPERCUSSAO_ID"
                            ],
                            "comunicacaoJudicial_id": self.i_intimacao["ID"],
                            "comunicacaoJucicial_numero": self.i_intimacao["NUMERO"],
                        }
                        # apresentando no log dados que foram ajustado para a requisição de distribuição de tarefa
                        self.__logger.log(
                            f"Preparando tarefa para intimação {self.i_intimacao['NUMERO']} para SETOR ID {self.i_intimacao['SETOR_ID']} "
                        )
                        self.__logger.log(
                            "-------------- Campos preenchidos da tarefa ------------------"
                        )
                        for key, value in tarefa.items():
                            self.__logger.log(f"{key}: {value}")
                        self.__logger.log(
                            "-------------- Fim dos Campos preenchidos da tarefa -----------"
                        )
                        requisicao = self.super_sapiens.create_tarefa(tarefa)

                        try:
                            resp_requisicao = json.loads(requisicao.content)
                            self.__logger.AbrirSeparador()
                            self.__logger.log("Resposta da requisição: ")
                            self.__logger.log(resp_requisicao.dumps())
                            self.__logger.FecharSeparador()
                        except Exception as e:
                            print(e)
                            self.__logger.FecharSeparador()

                        resultado_verificacao = self.__verificar_status_code(requisicao)
                        self.__logger.log(
                            f"Resultado da verificação: {resultado_verificacao}"
                        )

                        self.__logger.FecharSeparador()

                    # analisando o resultado
                    if resultado_verificacao != "OK" and contador < 3:
                        time.sleep(1)
                        contador += 1
                        continue
                    elif resultado_verificacao != "OK" and contador >= 3:
                        self.__verificar_erros(requisicao, self.dict_msg, etiquetar)
                        break

                    """
                    ###########
                    Entender melhor o pq disso
                    try:

                        if not resp_requisicao[0]['result']['0']['success']:
                            erro_usuario = resp_requisicao[0]['result']['0']['errors'][0]
                            erro = None
                            for chave, valor in erro_usuario.items():
                                erro = valor
                            erro_formatado = erro.upper()
                            self.i_intimacao['TEOR'] = ""
                            self.__form_etiqueta(f'R2D2: {erro_formatado}')
                            self.gravar_etiqueta(etiquetar)
                            self.status_processo['FLAG'] = f'{NAO_DISTRIBUIDO} - {erro_formatado}'
                            self.contador_relatorio['FLAG'] = NAO_DISTRIBUIDO
                    except Exception as e:
                    """

                    break
            # caso nao esteja ativo, marca info para relatorio
            else:
                """
                self.status_processo['FLAG'] = NAO_DISTRIBUIDO
                self.contador_relatorio['FLAG'] = NAO_DISTRIBUIDO
                """
                if "FLAG" not in self.status_processo:
                    self.status_processo["FLAG"] = NAO_DISTRIBUIDO
                if "FLAG" not in self.contador_relatorio:
                    self.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO

        # elif self.i_intimacao['NUP'] == "" and self.i_intimacao['ULTIMO_SETOR']:
        elif self.i_intimacao["NUP"] == "":
            ERRO_MENSAGEM = f"{NAO_DISTRIBUIDO} - SEM NUP"
            # ALTERADO POR FERNANDO PRU6 - NÃO ETIQUETAR NÃO DISTRIBUÍDO -SEM NUP
            # self.__form_etiqueta(f"R2D2: {ERRO_MENSAGEM}")
            self.gravar_etiqueta(etiquetar)
            if "FLAG" not in self.status_processo:
                self.status_processo["FLAG"] = ERRO_MENSAGEM
            if "FLAG" not in self.contador_relatorio:
                self.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO
        elif self.i_intimacao["SETOR_ID"] == "":
            if "FLAG" in self.contador_relatorio:
                if self.contador_relatorio["FLAG"] == ERRO_STATUSINTEGRACAO:
                    ERRO_MENSAGEM = f"{NAO_DISTRIBUIDO} - INTEGRAÇÃO COM FALHA"
                    self.__form_etiqueta(f"R2D2: {ERRO_MENSAGEM}")
                    if "FLAG" not in self.status_processo:
                        self.status_processo["FLAG"] = ERRO_MENSAGEM
            else:
                ERRO_MENSAGEM = f"{NAO_DISTRIBUIDO} - SEM SETOR ID"
                if "FLAG" not in self.status_processo:
                    self.status_processo["FLAG"] = ERRO_MENSAGEM
                if "FLAG" not in self.contador_relatorio:
                    self.contador_relatorio["FLAG"] = NAO_DISTRIBUIDO

        try:
            if ativo:
                # update = {
                #     "id_processo": self.i_intimacao["ID"],
                #     "numero": self.i_intimacao["NUMERO"],
                #     "numero_alternativo": self.i_intimacao["NUMERO_ALTERNATIVO"],
                #     "fonte_dados": self.i_intimacao["FONTE_DADOS"],
                #     "postit": self.i_intimacao["POSTIT"],
                #     "modalidade_repercucao_id": self.i_intimacao[
                #         "MODALIDADE_REPERCUSSAO_ID"
                #     ],
                #     "pasta_id": self.i_intimacao["PASTA_ID"],
                #     "id_comunicacao": self.i_intimacao["ID_COMUNICACAO"],
                #     "teor_gravar_etiqueta": self.i_etiqueta["TEOR"],
                #     "nivel_sigilo": self.i_intimacao["NIVEL_SIGILO"],
                #     "data_h_inicio_prazo": self.i_intimacao["DATA_H_INICIO_PRAZO"],
                #     "mod_com_jud_id": self.i_intimacao["MOD_COM_JUD_ID"],
                #     "setor_id_gravar_etiqueta": self.i_etiqueta["SETOR_ID"],
                #     "unidade_id": self.i_intimacao["UNIDADE_ID"],
                #     "proc_jud_id": self.i_intimacao["PROC_JUD_ID"],
                #     "status": self.i_intimacao["STATUS"],
                #     "status_integ": self.i_intimacao["STATUS_INTEG"],
                #     "criado_em": self.i_intimacao["CRIADO_EM"],
                #     "atualizado_em": self.i_intimacao["ATUALIZADO_EM"],
                #     "cent_distrib": self.i_intimacao["CENT_DISTRIB"],
                #     "digito_distrib": self.i_intimacao["DIGITO_DISTRIB"],
                #     "polo_entidade": self.i_intimacao["POLO_ENTIDADE"],
                #     "integracao": self.i_intimacao["INTEGRACAO"],
                #     "id_consultante": self.i_intimacao["ID_CONSULTANTE"],
                #     "idFormatado": (
                #         f"id: {self.i_intimacao['ID']} - "
                #         f"{self.i_intimacao['NUMERO']} - "
                #         f"{self.i_intimacao['MODALIDADE']} - "
                #         f"prazo final em {self.i_intimacao['DATA_H_FINAL_PRAZO']}"
                #     ),
                # }

                if self.sapiens_info["VERSAO"] == "SUPER":
                    pass

        except Exception as e:
            self.__logger.log(f"Erro no exception final gravar etiqueta : {str(e)}")

        # self.__logger.log("SAIU distribuidor Class Distribuidor gravar etiqueta")

    def ler_intimacoes(
        self, unidade_id: str, periodo: dict, total: bool, integrado: bool = None
    ) -> None:
        # print("ENTROO Distribuidor ler_intimações")

        try:
            if self.sapiens_info["VERSAO"] == "SUPER":
                parametros = {
                    "UNIDADES_IDS": unidade_id,
                    "INTEGRACAO": integrado,
                    "PERIODO": periodo,
                    "TOTAL": total,
                }
                retorno_sapiens = self.super_sapiens.get_comunicacao_judicial(
                    parametros
                )

            if total:
                self.total = retorno_sapiens[0]
            else:
                self.total = len(retorno_sapiens)
                self.resp_intimacoes = retorno_sapiens
            self.__logger.log("-" * 40)
            self.__logger.log(
                f"Foram encontradas {self.total} intimações nessa leitura"
            )
            self.__logger.log("-" * 40)
            self.__logger.log("Intimações sendo lidas do SuperSapiens...")
        except Exception as e:
            self.__logger.log("Ocorreu um erro")
            self.__logger.log(f"Exceção levantada foi {e}")


if __name__ == "__main__":
    pass
